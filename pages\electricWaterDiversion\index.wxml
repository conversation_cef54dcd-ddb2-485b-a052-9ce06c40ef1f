<!--pages/forestindex/index.wxml-->
<view>
  <view class="conectstate">
    <view class="goconnect">
    <image src="../../images/forest/blelogo.png" style="width:86rpx;height: 86rpx;" mode="widthFix"></image>
      <view wx:if="{{!isconnect}}" class="goconnect-btn" bindtap="goBleConnect">
        <text>连接设备</text>
        <image src="../../images/forest/goconnect.png" mode="widthFix"></image>
      </view>
      <view wx:if="{{isconnect}}" class="connected">
        <text style="font-family: PingFangSC-medium;font-size: 32rpx;color:#FFFFFF;">电动引水泵（DN50型）</text>
        <text style="font-family: PingFangSC-light;font-size: 24rpx;color:#FFFFFF;">设备型号</text>
      </view>
    </view>
    <view class="blestate">
      <view class="leftstate">
        <text wx:if="{{!isconnect}}" style="font-size:28rpx;font-family: PingFangSC-medium;">-</text>
        <view wx:if="{{isconnect}}" style="display: flex;align-items: center;justify-content: center;">
          <image src="../../images/forest/ze-checked.png" style="width:40rpx;height: 40rpx;" mode="widthFix"></image>
          <text style="margin-left: 5px;font-size:28rpx;font-family: PingFangSC-medium;">正常</text>
        </view>
        <text style="margin-top: 5px;font-size:20rpx;font-family: PingFangSC-light;">当前状态</text>
      </view>
      <view class="rightstate">
        <view class="bleconect" wx:if="{{!isconnect}}">
          <image src="../../images/forest/break.png" style="margin-right: 1px;width:40rpx;height: 40rpx;" mode="widthFix"></image>
          <text style="font-size:28rpx;font-family: PingFangSC-medium;">已断开</text>
        </view>
        <view class="bleconect" wx:if="{{isconnect}}" bindtap="disconnectble">
          <image src="../../images/forest/connectble.png" style="margin-right: 1px;width:40rpx;height: 40rpx;" mode="widthFix"></image>
          <text style="font-size:28rpx;font-family: PingFangSC-medium;">已连接</text>
        </view>
        <text style="margin-top: 5px;font-size:20rpx;font-family: PingFangSC-light;">蓝牙状态</text>
      </view>
    </view>
  </view>
  <view class="devicedata">
    <view class="datastate">
      <image src="../../images/forest/state1.png" style="width:78rpx;height: 78rpx;" mode="widthFix"></image>
      <view class="number">{{mpanum}}</view>
      <text class="unit">Mpa</text>
      <text class="unitname">（兆帕）</text>
    </view>
    <view class="datastate">
      <image src="../../images/forest/state1.png" style="width:78rpx;height: 78rpx;" mode="widthFix"></image>
      <view class="number">{{turnnum}}</view>
      <text class="unit">r/min</text>
      <text class="unitname">（转/分钟）</text>
    </view>
    <view class="datastate">
      <!-- <image src="../../images/forest/celsius.png" style="width:78rpx;height: 78rpx;" mode="widthFix"></image> -->
      <view>请输入时间</view>
      <!-- <view class="number">{{celsiusnum}}</view> -->
      <input type="number" class="number" style="width: 280rpx; text-align: center;" min="10" max="300" value="{{celsiusnum}}" />
      <text class="unit">S/秒</text>
      <text class="unitname">（摄氏度）</text>
    </view>
    <view class="datastate">
      <image src="../../images/forest/timer.png" style="width:78rpx;height: 78rpx;" mode="widthFix"></image>
      <view class="number">{{hournum}}</view>
      <text class="unit">h</text>
      <text class="unitname">（小时）</text>
    </view>
  </view>
  <view class="bntbox">
    <view class="bnttype">
      <view class="bnt" bindtouchend="startDeviceRelease" bindtouchstart="startDevicePress">启动</view>
    </view>
  </view>
</view>