const app = getApp()

function register() {
    var  date = new Date()
    var year = date.getFullYear()

    return year
}

function writeData (buf) {
    var that = this
    console.log("开始写入数据")
    var devId = app.globalData.devId
    var serviceId = "77617974-726F-6E69-6332-000248626C65"
    var charaId = "00010203-0405-0607-0809-0A0B0C0D2B19"
    var that = this

    // console.log(that.hexstringFromBuffer(buf))
    var buf1 = that.procData(buf).buffer;
    console.log(that.hexstringFromBuffer(buf1));

    wx.writeBLECharacteristicValue({
        deviceId: devId,
        serviceId: serviceId,
        characteristicId: charaId,

        value: buf1,
        success: function (res) {
            console.log('写入成功', res.errMsg)
        },
        fail: function (res) {
            console.log(res)
            if(res.errCode == 10006){
              console.log("写入数据失败,连接已断开");
              wx.closeBLEConnection({
                deviceId: devId,
                complete: function(res) {
                  wx.reLaunch({
                    url: '../index/index',
                  })
                },
              })
              
            }
        }
    })
}

function writeDataN(buf) {
    var that = this
    console.log("开始写入数据")
    var devId = app.globalData.devId
    var serviceId = "77617974-726F-6E69-6332-000248626C65"
    var charaId = "00010203-0405-0607-0809-0A0B0C0D2B19"
    var that = this

    var buf1 = buf.buffer;
    
    wx.writeBLECharacteristicValue({
        deviceId: devId,
        serviceId: serviceId,
        characteristicId: charaId,

        value: buf1,
        success: function (res) {
          console.log('写入成功', res.errMsg)
            app.globalData.isSendOK = true
        },
        fail: function (res) {
            console.log(res)
        }
    })
}

function writeData0(buf) {
    var that = this
    console.log("开始写入数据")
    var devId = app.globalData.devId
    var serviceId = "77617974-726F-6E69-6332-000248626C65"
    var charaId = "00010203-0405-0607-0809-0A0B0C0D2B19"
    var that = this

    // console.log(buf)
    var buf1 = buf.buffer;
    console.log(buf1);

    wx.writeBLECharacteristicValue({
        deviceId: devId,
        serviceId: serviceId,
        characteristicId: charaId,

        value: buf1,
        success: function (res) {
            console.log('写入成功', res.errMsg)
        },
        fail: function (res) {
            console.log(res)
        }
    })
}

function writeDataL(buf) {
    var that = this
    console.log("开始写入数据")
    var devId = app.globalData.devId
    var serviceId = "77617974-726F-6E69-6332-000248626C65"
    var charaId = "00010203-0405-0607-0809-0A0B0C0D2B19"
    // var that = this

  console.log(that.hexstringFromBuffer(buf))
    var buf0 = that.procData(buf);
    console.log(buf0);
    var buf1 = new Uint8Array(20)
    var buf2 = new Uint8Array()
    if(buf0.length > 20){
        for(var i=0;i<20;i++){
            buf1[i] = buf0[i]
        }

        buf2 = buf0.slice(20)
        
        console.log(buf2)
    }else{
        buf1 = buf0
    }

    console.log(buf0.length)

    wx.writeBLECharacteristicValue({
        deviceId: devId,
        serviceId: serviceId,
        characteristicId: charaId,

        value: buf1.buffer,
        success: function (res) {
            console.log('写入成功', res.errMsg)
            that.writeData0(buf2)
        },
        fail: function (res) {
            console.log(res)
        }
    })
}


function reconnectBle(devId) {
    var that = this;
    //获取适配器
    wx.openBluetoothAdapter({
        success: function (res) {
            // success
            console.log("-----success----------");
            console.log(res);
            //开始搜索
            wx.startBluetoothDevicesDiscovery({
                services: [],
                success: function (res) {
                    // success
                    console.log("-----startBluetoothDevicesDiscovery--success----------");
                    console.log(res);

                    wx.onBluetoothDeviceFound(function (obj) {
                        
                        if (obj.devices[0].name) {
                            obj.devices.map(dev => {
                                if (dev.deviceId == devId){
                                    that.connectBle(devId)
                                }
                            })
                        }
                        
                    })
                },
                fail: function (res) {
                    // fail
                    console.log(res);
                },
                complete: function (res) {
                    // complete
                    console.log(res);
                }
            })
        },
        fail: function (res) {

            wx.showModal({
                title: '提示',
                content: "请检测手机蓝牙是否打开",
                showCancel: false
            })
        },
    })
}

function connectBle(devId) {
    var that = this
    //点击连接设备
    wx.createBLEConnection({
        deviceId: devId,
        success: function (res) {
            console.log(res)
            var serviceId, charaId
            //获取服务
            wx.getBLEDeviceServices({
                deviceId: devId,
                success: function (res) {
                    // sendId = devId;
                    // console.log(sendId);
                    //console.log(sendId);
                    serviceId = "77617974-726F-6E69-6332-000248626C65"

                    //获取特征值
                    wx.getBLEDeviceCharacteristics({
                        deviceId: devId,
                        serviceId: serviceId,

                        success: function (res) {
                            console.log('device getBLEDeviceCharacteristics:', res.characteristics)

                            if (that.userBleConnected) {
                                that.userBleConnected(res)
                            }

                            // charaId = res.characteristics[0].uuid
                            // console.log(charaId)
                            charaId = "00010203-0405-0607-0809-0A0B0C0D2B17"
                            that.listener(devId, serviceId, charaId)
                        }
                    })
                }
            })
        },
        fail: res => {
            //连接失败
            console.log(res)
          if (that.userBleConnectFailed) {
            that.userBleConnectFailed(res)
          }
          
        }
    })
}

function listener(devId, serviceId, charaId) {
    var that = this
    wx.notifyBLECharacteristicValueChange({
        state: true,
        deviceId: devId,
        serviceId: serviceId,
        characteristicId: charaId,
        success: function (res) {
            console.log(res)
        },
        fail(res) {
            console.log(res);
        }
    })
    wx.onBLECharacteristicValueChange(function (res) {

        var bufx = new Uint8Array(res.value);
        // var bufView = that.checkData(buf);

        // console.log('收到数据', bufView)
        if(__wxConfig.envVersion == "develop"){
            console.log('收到新数据--', that.hexstringFromBuffer(bufx));
        }        

        if(bufx.length >=3 && bufx[3] == 0xb2 && that.voiceCallback){
            //如果语音管理存在,并且也返回了b2,就不放在控制处理;
            //这是因为查询语音列表和播放语音的协议,都是B2才产生的问题.问题id=81;
            //这种做法不能根本解决问题,只要播放时间足够短,并且设备语音足够多,就很容易出现.
        }else if (that.userGetdataCallback) {
            that.userGetdataCallback(res)
        }

        if (that.triggerCallback) {
            that.triggerCallback(res)
        }

        if (that.playCallback) {
            that.playCallback(res)
        }

        if (that.voiceCallback) {
            that.voiceCallback(res)
        }

        if (that.batterryCallback) {
            that.batterryCallback(res)
        }

        if (that.blenameCallback) {
            that.blenameCallback(res)
        }

        if (that.passwordCallback) {
            that.passwordCallback(res)
        }

        if (that.customizeCallback) {
            that.customizeCallback(res)
        }

        if (that.timerCallback) {
            that.timerCallback(res)
        }

        if (that.docCallback) {
            that.docCallback(res)
        }
        if(that.voiceChangedCallback){
          that.voiceChangedCallback(res)
        }
        if(that.settingListCallback){
            that.settingListCallback(res)
        }
        if(that.languageSettingCallback){
            that.languageSettingCallback(res);
        }
    })

    app.globalData.devId = devId
    // wx.hideLoading()
}

 function procData(buf) {
    var i, length, num;
    length = buf.length;

    num = 0;
    var buf1 = new Uint8Array(length * 2);

    for (i = 0; i < length; i++) {
        if (buf[i] == 0x70) {
            buf1[num] = 0x70;
            num++;
            buf1[num] = 0x00;
            num++;
        } else if (buf[i] == 0x7A) {
            buf1[num] = 0x70;
            num++;
            buf1[num] = 0x0A;
            num++;

        } else if (buf[i] == 0x7E) {
            buf1[num] = 0x70;
            num++;
            buf1[num] = 0x0E;
            num++;

        } else {
            buf1[num] = buf[i];
            num++;
        }
    }

    var buf0 = new Uint8Array(num + 4);
    buf0[0] = 0x7E;
    buf0[1] = (num - 2) >> 8
    buf0[2] = num - 2;

    for (var k = 0; k < num; k++) {
        buf0[k + 3] = buf1[k];
    }

    buf0[num + 3] = 0x7A;

    return buf0;
}

function checkData (buf) {
    var num = 1;
    var k = 1;
    var buf0 = new Uint8Array(buf.length);
    buf0[0] = buf[0];
    while (1) {
        if (buf[k] == 0x70) {
            buf0[num] = buf[k] + buf[k + 1];
            k += 2;
            num++;
        } else {
            buf0[num] = buf[k];
            k++;
            num++;
        }
        if (k >= (buf.length - 1)) break;
    }
    buf0[num] = buf[k];
    num++;

    var buf2 = new Uint8Array(num);
    for (var i = 0; i < num; i++) {
        buf2[i] = buf0[i];
    }

    return buf2;
}
/**
 * 查询小程序功能配置.
 */
function queryConfigInner(){
    var that = this;
    console.log("查询小程序功能配置");
    var num = 3;
    var buf0 = new Uint8Array(num);
    buf0[0] = 0XDD
    var tem = buf0[0];
    for (var i = 1; i < (num - 2); i++) {
        tem += buf0[i];
    }

    buf0[num - 2] = tem >> 8
    buf0[num - 1] = tem
    that.writeData(buf0);
}
/**
 * 查询小程序功能配置.
 */
function queryConfig(callback){
    var that = this;
    //读取缓存配置;
    var saveConfig = wx.getStorageSync("config");
    if(saveConfig == ""){
        saveConfig = [];
    }
    saveConfig = saveConfig.filter(function(obj,index){
        return obj.devId == app.globalData.devId;
    });
    if(saveConfig.length > 0){        
        callback.success(saveConfig[saveConfig.length - 1]);
        //这是当前设备的配置,只有一条数据;
        app.globalData.config = saveConfig[saveConfig.length - 1];
        return;
    }    
    //没有缓存,读取设备配置;
    that.queryConfigInner();
    var time = 0;
    var interval = setInterval(function () {
            // var config = app.globalData.config;
            // config = config.filter(function(obj,index){
            //     return obj.devId == app.globalData.devId;
            // });
            // if (config.length == 0) 
            if(app.globalData.config == null)
            {
                time++;
                if (time < 3) {
                    that.queryConfigInner();//重试
                }else {
                    clearInterval(interval);//查询不到,放弃了.
                    callback.fail();
                    that.firstSetBroadcastLanguage();//失败也不影响设置语言;
                }
            }
            else {
                clearInterval(interval);//查询到结果了
                // callback.success(config[config.length - 1]);
                callback.success(app.globalData.config);
            }
    },2000);
}
/**首次连接时,自动设置语言 */
function firstSetBroadcastLanguage(index){    
    var that = this;
    var userIndex = wx.getStorageSync("userSetLanguage" + app.globalData.devId);
    if(userIndex !== "" && userIndex !== null){
        return;
    }
    console.log("首次连接时,自动设置语言");
    var buffer = [0xA8, index, 0x00, 0xA8 + index];
    that.writeData(buffer);
    var time = 0;
    var interval = setInterval(function () {
        userIndex = wx.getStorageSync("userSetLanguage" + app.globalData.devId);
        if(userIndex !== "" && userIndex !== null){
          clearInterval(interval);
          wx.hideLoading();
          return;
        }
        time++;
        if (time < 3) {
            that.writeData(buffer);
        }
        else {
            clearInterval(interval);
        }
    }, 1000 + (time * 500));
}
function bufferFormArray(array) {
  return new Uint8Array(array).buffer;
}
function arrayFromBuffer (arrayBuffer) {
  return Array.prototype.slice.call(new Uint8Array(arrayBuffer));
}
function hexstringFromBuffer (buffer) { // buffer is an ArrayBuffer
  return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('');
}
function bufferFormHexstring (hex) {
  if (hex.trim().length == 0) {
    return new ArrayBuffer();
  }
  var typedArray = new Uint8Array(hex.match(/[\da-f]{2}/gi).map(function (h) {
    return parseInt(h, 16)
  }))
  return typedArray.buffer;
}

module.exports = {
    writeData: writeData,
    writeDataN: writeDataN,
    writeData0: writeData0,
    writeDataL: writeDataL,
    register: register,
    reconnectBle: reconnectBle,
    connectBle: connectBle,
    listener: listener,
    procData: procData,
    checkData: checkData,
    bufferFormArray: bufferFormArray,
    arrayFromBuffer: arrayFromBuffer,
    hexstringFromBuffer: hexstringFromBuffer,
    bufferFormHexstring: bufferFormHexstring,
    queryConfig:queryConfig,
    queryConfigInner:queryConfigInner,
    firstSetBroadcastLanguage:firstSetBroadcastLanguage,
}