// pages/voicechange/voicechange.js
const app = getApp()
var user = require('../../utils/ble.js');
var devId;
var time = 0;
var sendnum = 0;
Page({

    /**
     * 页面的初始数据
     */
    data: {
        filedata:[],
        interval: "",      //定时器
        isStart:false,
        isSend:false,
        isEnd:false,
        bufE0:[]
    },

    getDoc:function(){
        var that = this
        wx.chooseMessageFile({
            count: 1,
            type: 'all',
            success: function (res) {
                console.log(res.tempFiles[0])

                var str = res.tempFiles[0].path
                var fil = wx.getFileSystemManager()
                fil.readFile({
                    filePath: str,
                    encoding: 'base64',
                    success: function (result) {
                        console.log(result)
                        var buf = new Uint8Array(wx.base64ToArrayBuffer(result.data));
                        console.log(buf)
                        that.data.filedata = buf
                        console.log(that.data.filedata)
                        var len = buf.length
                        var tmp = 0
                        for(var i=0;i<len;i++){
                            tmp += buf[i]
                        }

                        var bufsend = new Uint8Array(11)
                        bufsend[0] = 0xE0;
                        bufsend[1] = len >> 24;
                        bufsend[2] = len >> 16;
                        bufsend[3] = len >> 8;
                        bufsend[4] = len;
                        bufsend[5] = tmp >> 24;
                        bufsend[6] = tmp >> 16;
                        bufsend[7] = tmp >> 8;
                        bufsend[8] = tmp;

                        var buftmp = 0
                        for(var i=0;i<9;i++){
                            buftmp += bufsend[i]
                        }

                        bufsend[9] = buftmp >> 8;
                        bufsend[10] = buftmp;

                        that.data.bufE0 = bufsend
                        console.log(that.data.bufE0)
                        user.writeData(that.data.bufE0)

                        that.data.isStart = true

                        time = 0;
                        that.startTap()

                        wx.showLoading({
                          title: app.globalData.i18n["voicemanager.deleting"],
                            mask: true
                        })
                    }
                })
            },
            fail: function (err) {
                console.log(err);
            },
        });
    },

    init: function (that) {
        var time = 60;
        var interval = ""
        that.clearTimeInterval(that)
        that.setData({
            time: time,
            interval: interval,
        })
    },


    clearTimeInterval: function (that) {
        var interval = that.data.interval;
        clearInterval(interval)
    },

    restartTap: function () {
        var that = this;
        that.init(that);
        console.log("倒计时重新开始")
        that.startTap()
    },

    startTap: function () {
        var that = this;
        that.init(that);          //这步很重要，没有这步，重复点击会出现多个定时器

        var interval = setInterval(function () {
            if (that.data.isStart) {
                time++;
                if ((time/25) < 3) {
                    if((time%25 == 0)){
                        user.writeData(that.data.bufE0)
                    }
                }
                else {
                    that.data.isStart = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    if (wx.getStorageSync("language") == "zh_CN") {
                      wx.showToast({
                        title: app.globalData.i18n["control.tips.sendingFailed"],
                        image: '/images/error.png',
                        duration: 1000
                      })
                    } else {
                      wx.showModal({
                        title: 'Tips',
                        content: app.globalData.i18n["control.tips.sendingFailed"],
                        showCancel: false,
                        confirmText: "OK"
                      })
                    }
                }
            }
            if(that.data.isSend){
                if (app.globalData.isSendOK){
                    sendnum++;
                    console.log(sendnum)
                    if(sendnum*20 >= that.data.filedata.length){
                        that.sendE1()
                        that.data.isSend = false
                        that.data.isEnd = true
                    }else{
                        that.sendDoc(sendnum)
                        
                    }
                }
            } 
        }, 20)

        that.setData({
            interval: interval
        })
    },

    sendE1:function(){
        var buffer = [0xE1, 0x00, 0xE1];
        user.writeData(buffer)
    },

    sendDoc:function(num){
        var tmp = num * 20
        if ((this.data.filedata.length - tmp) >= 20){
            var buf = new Uint8Array(20)
            
            for(var i=0;i<20;i++){
                buf[i] = this.data.filedata[tmp + i]
            }
            user.writeDataN(buf)
        }else{
            var num = this.data.filedata.length - tmp
            var buf = new Uint8Array(num)
            for (var i = 0; i < num; i++) {
                buf[i] = this.data.filedata[tmp + i]
            }
            user.writeDataN(buf)
        }
        
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this;
        this.setData({
          i18n:app.globalData.i18n,
        })
        //=======================================================
        //recieve data
      user.voiceChangedCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据d', user.hexstringFromBuffer(buf))

            if(that.data.isStart){
                if ((bufView[0] == 0x7E) && (bufView[3] == 0xE0)) {
                    that.data.isStart = false

                    sendnum = 0;
                    time = 0;
                    that.data.isSend = true
                    app.globalData.isSendOK = false
                    that.sendDoc(sendnum)
                }
            }

            if (that.data.isEnd){
                if ((bufView[0] == 0x7E) && (bufView[3] == 0xE1)) {
                    wx.hideLoading()
                    that.data.isEnd = false
                    
                    wx.showToast({
                      title: app.globalData.i18n["SendOK"],
                        duration:1000
                    })
                }
            }
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.voiceChangedCallback = null;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})