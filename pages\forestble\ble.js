// pages/forestble/ble.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    bartitle: '搜索设备',
    isScan: false,
    lastRefreshTimestamp: 0,
    scanDevices: [],
    i18n: []
  },
  // 点击连接蓝牙
  connectBlu(e) {
    console.log('点击链接', e)
    var _this = this
    var devId = e.currentTarget.dataset.dev.deviceId;
    var devName = e.currentTarget.dataset.dev.name;
    console.log(_this.data.scanDevices)
    wx.stopBluetoothDevicesDiscovery({
      success: function (res) {
        console.log(res)
        console.log('开始连接')
        app.globalData.devId = devId
        app.globalData.devName = devName;
        let pages = getCurrentPages();
        let prevPage = pages[pages.length - 2]; //获取A页面
        wx.navigateBack({
          delta: 1,
          success: function (e) { // 成功的回调
            if (prevPage == undefined || prevPage == null) return;
            prevPage.bleCallBack();  // 调用A页面的方法, 并将值传过去
          }
        })
      }
    })
  },
  // 搜索设备
  searchtouch: function () {
    var that = this;
    wx.stopBluetoothDevicesDiscovery({
      complete: (res) => {
        console.log('stopBluetoothDevicesDiscovery', res)
        wx.closeBluetoothAdapter({
          complete: (res) => {
            console.log('closeBluetoothAdapter', res)
            //获取适配器
            wx.openBluetoothAdapter({
              success: function (res) {
                console.log('openBluetoothAdapter', res)
                // success
                console.log("-----success----------");
                console.log(res);
                //开始搜索
                wx.startBluetoothDevicesDiscovery({
                  services: ["0000ffe0-0000-1000-8000-00805f9b34fb"],
                  allowDuplicatesKey: true,
                  success: function (res) {
                    // success
                    console.log("-----startBluetoothDevicesDiscovery--success----------");
                    console.log(res);
                    wx.onBluetoothDeviceFound(function (obj) {
                      console.log('搜索到设备!!!', obj.deviceId);
                      var temp = that.data.scanDevices
                      var needResort = false;
                      for (var indexx = 0; indexx < obj.devices.length; indexx++) {
                        var dev = obj.devices[indexx];
                        // 添加名称过滤逻辑 - 只保留名称包含特定字符串的设备
                        if (!dev.name || dev.name == "" || (dev.name != "XLBLE" && dev.name != "高压灭火水泵")) {
                          continue;
                        }
                        let pDev = temp.findIndex((it) => {
                          return it.deviceId === dev.deviceId
                        })
                        if (pDev == -1) {
                          if (dev.RSSI == 127) {
                            continue;
                          }
                          var userIndex = wx.getStorageSync("userSetLanguage" + dev.deviceId);
                          console.log('userIndex', userIndex);
                          if (userIndex !== "" && userIndex !== null) {
                            dev.lastConnect = 1;
                          }
                          dev.scanTime = new Date().getTime();
                          temp.push(dev)
                          needResort = true;
                          console.log(dev);
                        } else {
                          if (temp[pDev].RSSI != dev.RSSI) {
                            needResort = true;
                          }
                          if (dev.RSSI == 127) {
                            temp.slice(pDev, 1);
                            continue;
                          }
                          // dev.RSSI = parseInt(((temp[pDev].RSSI + dev.RSSI)/2));
                          dev.scanTime = new Date().getTime();
                          dev.lastConnect = temp[pDev].lastConnect;
                          temp[pDev] = dev;
                          // temp.slice(pDev,1,dev);
                        }
                      }
                      if (needResort) {
                        temp = temp.sort(function (a, b) {
                          return a.RSSI > b.RSSI ? -1 : 1;
                        });
                        let nowDateTime = (new Date()).getTime();
                        if (that.data.lastRefreshTimestamp == 0 || (nowDateTime - that.data.lastRefreshTimestamp) > 1500) {
                          that.data.lastRefreshTimestamp = nowDateTime;
                          that.setData({
                            scanDevices: temp,
                            bartitle: '设备列表',
                            isScan: true
                          })
                          console.log('搜索到的结果', that.data.scanDevices)
                        }
                      }
                    })
                  },
                  fail: function (res) {
                    // fail
                    console.log("蓝牙扫描失败");
                    console.log(res);
                    wx.stopPullDownRefresh();
                    that.setData({
                      isPullingDown: false,
                    })
                  },
                  complete: function (res) {
                    // complete
                    console.log(res);

                  }
                })
              },
              fail: function (res) {

                if (wx.getStorageSync("language") == "zh_CN") {
                  wx.showModal({
                    title: app.globalData.i18n["Tips"],
                    content: app.globalData.i18n["bt.blecheck"],
                    showCancel: false
                  })
                } else {
                  wx.showModal({
                    title: 'Tips',
                    content: app.globalData.i18n["bt.blecheck"],
                    showCancel: false,
                    confirmText: "OK"
                  })
                }
                wx.stopPullDownRefresh();
                that.setData({
                  isPullingDown: false,
                })
              },
            })
          },
        })
      },
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.searchtouch()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})