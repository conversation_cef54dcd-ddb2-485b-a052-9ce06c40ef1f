/* pages/forestble/ble.wxss */
page {
  background-color: #ffffff;
}

.toptitle {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  color: rgba(153, 159, 159, 1);
  font-family: PingFangSC-extraLight;
}

.circleBox {
  position: relative;
  top: 300rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 600rpx;
}

/* 扩散动画 */
.circle,
.circle1,
.circle2,
.circle3 {
  width: 100px;
  height: 100px;
  /* height: 10px; */
  background: rgba(12, 107, 58, .8);
  /* border: 1px solid rgba(35,147,255,0.2);; */
  border-radius: 999px;
  position: absolute;
}

.centericon {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
  width: 90rpx;
  height: 90rpx;
  border-radius: 100px;
  background-color: #ffffff;
}

.circle-img {
  /* position: absolute; */
  /* z-index: 99; */
  width: 48rpx;
  height: 48rpx;
}

.text-circl {
  position: absolute;
  top: 300px;
  width: 100%;
  color: rgba(12, 107, 58, 1);
  font-size: 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-family: PingFangSC-regular;
}

.devicelist {
  width: 92%;
  margin: auto;
}
.itemcell{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.deviceitem {
  margin-top: 17px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 130rpx;
  border-radius: 6px;
  background-color: rgba(230, 230, 230, 0.3);
}
.leftstate{
  display: flex;
  align-items: center;
}

.rightstate{
  margin-right: 9px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.itemicon {
  margin-left: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 78rpx;
  height: 66rpx;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 1);
}

.circle1,
.circle2,
.circle3 {
  /* animation: circleChange 2s 1s ease-out infinite; */
  animation-name: circleChange;
  animation-duration: 3s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}

.circle1 {
  animation-delay: 1s;
}

.circle2 {
  animation-delay: 2s;
}

.circle3 {
  animation-delay: 3s;
}

/* 旋转竖线样式 */
.rotating-line {
  position: absolute;
  width: 15rpx;
  top: -35rpx;
  height: 325rpx;
  background-color: rgba(12, 107, 58, 0.8);
  z-index: 98;
  border-radius: 21px;
  animation: rotateLine 10s linear infinite;
  transform-origin: bottom;
}

@keyframes circleChange {
  0% {
    transform: scale(1);
    opacity: 0.95;
  }

  /* 25% {
    transform: scale(2);
    opacity: 0.75;
  } */

  50% {
    transform: scale(2);
    opacity: 0.5;
  }

  /* 75% {
    transform: scale(4);
    opacity: 0.25;
  } */

  100% {
    transform: scale(3);
    opacity: 0.05;
  }
}

@keyframes rotateLine {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}