/* pages/forestindex/index.wxss */
page {
    background-color: #ffffff;
    padding-top: 20rpx;
  }
  
  .conectstate {
    position: relative;
    width: 690rpx;
    height: 380rpx;
    margin: auto;
    border-radius: 10px;
    background-color: #0C6B3A;
    padding: 42rpx 26rpx 0;
    box-sizing: border-box;
  }
  
  .goconnect {
    display: flex;
    align-items: flex-end;
    width: 100%;
    color: #FFFFFF;
    font-size: 32rpx;
    font-family: PingFangSC-medium;
  }
  .goconnect-btn {
    padding-bottom: 8rpx;
    height: 46rpx;
    line-height: 46rpx;
    display: flex;
    align-items: center;
    padding-left: 34rpx;
    font-size: 32rpx;
    color: #fff;
  }
  .goconnect-btn image {
    width: 48rpx;
    height: 48rpx;
    margin-left: 8rpx;
  }
  .connected{
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .blestate {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 144rpx;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, .3);
    color: rgba(16, 16, 16, 1);
    margin-top: 42rpx;
  }
  
  .leftstate {
    display: flex;
    margin-left: 27px;
    flex-direction: column;
    align-items: center;
    color: #FFFFFF;
  }
  
  .rightstate {
    display: flex;
    margin-right: 17px;
    flex-direction: column;
    align-items: center;
    color: #FFFFFF;
  }
  .bleconect{
     display: flex;
     align-items: center;
  }
  

  /* */

  .controlbox {
    width: 690rpx;
    margin: auto;
    margin-top: 42rpx;
  }

  .controlbox-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .controlbox-top-item {
    width: 330rpx;
    height: 114rpx;
    border-radius: 16rpx;
    background-color: rgba(230,230,230,0.3);
    border: 2rpx solid rgba(12,107,58,1);
    display: flex;
    box-sizing: border-box;
    padding: 0 24rpx 0 32rpx;
    align-items: center;
    justify-content: space-between;
    line-height: 40rpx;
    color: rgba(16,16,16,1);
    font-size: 28rpx;
    text-align: center;
    font-family: PingFangSC-regular;
  }

  .controlbox-top-item-switch {
    width: 74rpx;
    height: 40rpx;
    background: #b5b5b5;
    border-radius: 40rpx;
    position: relative;
  }

  .controlbox-top-item-switch::after {
    content: ' ';
    position: absolute;
    width: 30rpx;
    height: 30rpx;
    top: 50%;
    left: 5rpx;
    transition: all 0.3s;
    transform: translate(0, -50%);
    background: #fff;
    border-radius: 50%;
  }

  .controlbox-top-item-icon {
    width: 56rpx;
    height: 56rpx;
  }

  .controlbox-top-item-switch-on {
    background: rgba(12,107,58,1);
  }

  .controlbox-top-item-switch-on::after {
    content: ' ';
    position: absolute;
    width: 30rpx;
    height: 30rpx;
    top: 50%;
    left: 39rpx;
    transform: translate(0, -50%);
    background: #fff;
    border-radius: 50%;
  }

  /**/
  .controlbox-play {
    width: 356rpx;
    height: 356rpx;
    border: 2rpx solid rgba(127,177,110,1);
    border-radius: 50%;
    margin: 112rpx auto 0;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .controlbox-play-btn {
    width: 168rpx;
    height: 168rpx;
    border-radius: 50%;
    background-color: rgba(241,241,241,1);
    border: 2rpx solid rgba(127,177,110,1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .controlbox-play-btn image {
    width: 48rpx;
    height: 48rpx;
  }

  .controlbox-play-btn-add-icon {
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    top: 18rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .controlbox-play-btn-minus-icon {
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    bottom: 18rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .controlbox-play-btn-next-icon {
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    right: 18rpx;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .controlbox-play-btn-prev-icon {
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    left: 18rpx;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
  }



  /**/
  .controlbox-bottom {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, auto);
    column-gap: 18rpx;
    row-gap: 26rpx;
    width: 690rpx;
    margin: 108rpx auto 0;
  }

  .controlbox-bottom-item {
    box-sizing: border-box;
    height: 138rpx;
    border-radius: 20rpx;
    background-color: rgba(230,230,230,0.3);
    border: 2rpx solid rgba(12,107,58,1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    line-height: 40rpx;
    color: rgba(16,16,16,1);
    font-size: 28rpx;
  }

  .controlbox-bottom-item-icon {
    width: 46rpx;
    height: 48rpx;
    margin-bottom: 10rpx;
  }


  /**/
  
  .bntbox {
    display: flex;
    align-items: center;
    width: 690rpx;
    height: 162rpx;
    margin: auto;
    margin-top: 17px;
    border-radius: 50px;
    background-color: rgba(12, 107, 58, 1);
  }
  
  .bnttype {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    width: 96%;
    height: 146rpx;
    margin: auto;
    border-radius: 50px;
    border: 1px solid rgba(230, 230, 230, 1);
    color: rgba(255, 255, 255, 1);
    font-size: 36rpx;
    font-family: PingFangSC-semiBold;
  }
  
  .segmentation {
    width: 2rpx;
    height: 78rpx;
    background-color: rgba(255, 255, 255, 1);
  }
  
  .bnt {
    width: 40%;
    height: 78rpx;
    line-height: 78rpx;
    text-align: center;
  }