// pages/forestindex/index.js
const app = getApp()
// var user = require('../../utils/forestble.js');
var user = require('../../utils/ble.js');
var time = 0;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isconnect: false,
    isON: true,

    isPlay: false,
    isStopPlay: false,
    isStartPlay: false,
  },

  /**
   * 前往搜索蓝牙设备连接
   */
  goBleConnect() {
    wx.navigateTo({
      url: '../forestble/ble',
      success: () => {
        console.log('跳转蓝牙页面')
      }
    })
  },
  disconnectble() {
    var that = this
    user.clearAll();
    // 清除所有回调
    user.userGetdataCallback = null;
    user.userBleConnectFailed = null;
    user.userBleConnected = null;
    // 取消所有监听
    wx.offBLECharacteristicValueChange();
    wx.offBLEConnectionStateChange();
    wx.offAppShow();
    // 先停止搜索再断开
    wx.stopBluetoothDevicesDiscovery({
      complete: () => {
        wx.closeBLEConnection({
          deviceId: app.globalData.devId,
          complete: function (res) {
            // 重置全局变量
            app.globalData.devId = '';
            that.setData({
              isconnect: false,
              mpanum: 0,
              turnnum: 0,
              celsiusnum: 0,
              hournum: 0
            });
            console.log('已完全断开连接');
          }
        });
      }
    });
  },


  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('设备详情页面信息', options)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时检查连接状态
    if (app.globalData.devId && this.data.isconnect) {
      this.setData({
        isconnect: true
      })
    }
  },


  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    wx.offAppShow();
    // 调用清理方法
    user.clearAll();
    user.userGetdataCallback = null;
    wx.offBLECharacteristicValueChange();
    wx.offBLEConnectionStateChange();
    wx.stopBluetoothDevicesDiscovery({
      complete: () => {
        if (app.globalData.devId) {
          wx.closeBLEConnection({
            deviceId: app.globalData.devId,
            complete: () => {
              app.globalData.devId = '';
            }
          });
        }
      }
    });
  },

  changOnOff() {
    if (!this.data.isconnect) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return;
    }
    this.setData({
      isON: !this.data.isON
    })
  },
  /**
   * 定时播放
   */
  changetimePlay:function(){
    if (!this.data.isconnect) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return;
    }
    wx.switchTab({
      url: '/pages/timer/timer',
    })
  },

  changeplay: function () {
    if (!this.data.isconnect) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return;
    }
    if (this.data.isON) {
        var that = this

        if (that.data.isPlay) {
            that.data.isStopPlay  = true;

            var buffer = [0xc2, 0x00, 0x00, 0xc2];
            user.writeData(buffer)

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isStopPlay && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {
                        user.writeData(buffer)
                    }
                    else {
                        that.data.isStopPlay = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        that.showSendingFailed();
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        } else {

            this.setData({
                isStartPlay: true,
            })

            var buffer = [0xc2, 0x01, 0x00, 0xc3];
            user.writeData(buffer)

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isStartPlay && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {
                        user.writeData(buffer)
                    }
                    else {
                        clearInterval(interval);
                        wx.hideLoading();
                        that.showSendingFailed();
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }
    }
  },
  showSendingFailed:function(){
    wx.showToast({
      title: app.globalData.i18n["control.tips.sendingFailed"],
      image: '/images/error.png',
      duration: 1000
    })
  },
})