// pages/bt/bt.js
const app = getApp()
var user = require('../../utils/ble.js');
var devId;
var time = 0;
// var base64Images = require('../../images/base64')
Page({

    /**
     * 页面的初始数据
     */
    data: {
        debug: __wxConfig.envVersion,
        lastRefreshTimestamp:0,
        isPullingDown: false,
        password: "",
        scanDevices: [],
        borded: "已配对的设备",
        unborded: "可用设备",
        ReData: "1234",
        scanTimeout: false,
        // connectIcon:base64Images.icon20,
        hiddenmodal: true,
        sacanTime:0,
        listData: [{
                "code": "01",
                "text": "text1",
                "type": "type1"
            },
            {
                "code": "02",
                "text": "text2",
                "type": "type2"
            },
            {
                "code": "03",
                "text": "text3",
                "type": "type3"
            },
            {
                "code": "04",
                "text": "text4",
                "type": "type4"
            },
            {
                "code": "05",
                "text": "text5",
                "type": "type5"
            },
            {
                "code": "06",
                "text": "text6",
                "type": "type6"
            },
            {
                "code": "07",
                "text": "text7",
                "type": "type7"
            }
        ],

        isLogin: false,
        i18n: [],
        timeoutNumber: -1,
    },

    getInput: function (e) { //方法1
        this.setData({
            password: e.detail.value
        });
    },

    //取消按钮
    cancel: function () {
        wx.closeBLEConnection({
            deviceId: app.globalData.devId,
            success: function (res) {
                console.log("Close BlE success!")
            },
        })

        this.setData({
            hiddenmodal: true
        });
    },

    //确认
    confirm: function () {
        var that = this

        if (this.data.password.length == 6) {
            var str = this.data.password
            app.globalData.password = str

            var buf0 = new Uint8Array(str.length + 3);
            buf0[0] = 0xD1
            var tem = buf0[0];
            for (var i = 0; i < str.length; i++) {
                buf0[i + 1] = str.charCodeAt(i);
                tem += buf0[i + 1];
            }

            buf0[str.length + 1] = tem >> 8
            buf0[str.length + 2] = tem

            user.writeData(buf0)

            that.data.isLogin = true;

            time = 0;
            var interval = setInterval(function () {
                if (app.globalData.isLogin) {
                    time++;
                    if (time < 3) {

                        user.writeData(buf0)
                    } else {
                        that.data.isLogin = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        wx.showToast({
                            title: app.globalData.i18n["control.tips.loginerror"],
                            duration: 1000,
                            image: '/images/error.png',
                        })
                    }
                } else {
                    clearInterval(interval);
                }

            }, 1000)

            wx.showLoading({
                title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        } else {
            wx.showToast({
                title: app.globalData.i18n["control.tips.password"],
                image: '/images/error.png',
                duration: 1000
            })
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this;
        
        this.setData({
            i18n: app.globalData.i18n,
            borded: app.globalData.i18n["bt.borded"],
            unborded: app.globalData.i18n["bt.unborded"],
        });
        wx.setNavigationBarTitle({
            title: this.data.i18n["navBar.Title"],
          });
        setInterval(() => {
            that.setData({
                scanTime:(new Date().getTime()),
            });
        }, 1000);

        this.refreshDeviceList();
        
    },

    refreshDeviceList: function () {
        var that = this;
        if (this.data.timeoutNumber > 0) {
            clearTimeout(this.data.timeoutNumber);
        }

        that.setData({
            scanDevices: [],
            scanTimeout: false,
            isPullingDown: true,
        })
        if(wx.offBluetoothDeviceFound != undefined){
            wx.offBluetoothDeviceFound(function () {

            });
        }

        wx.stopBluetoothDevicesDiscovery({
            complete: (res) => {
                wx.closeBluetoothAdapter({
                    complete: (res) => {
                        //获取适配器
                        wx.openBluetoothAdapter({
                            success: function (res) {
                                // success
                                console.log("-----success----------");
                                console.log(res);
                                // //获取已经搜索的记录.
                                // wx.getBluetoothDevices({
                                //     success: function (res) {
                                //         var temp = [];
                                //         res.devices.forEach(device => {
                                //             if (device.localName != undefined && (device.localName == null || device.localName.length != 0)) {
                                //                 temp.push(device);
                                //             }
                                //         });
                                //         temp = temp.sort(function (a, b) {
                                //             return a.RSSI > b.RSSI ? -1 : 1;
                                //         });
                                //         that.setData({
                                //             scanDevices: temp
                                //         })
                                //     },
                                // })
                                //开始搜索
                                wx.startBluetoothDevicesDiscovery({
                                    services: ["656C6248-0200-3263-696E-6F7274796177"],
                                    allowDuplicatesKey:true,
                                    success: function (res) {
                                        // success
                                        console.log("-----startBluetoothDevicesDiscovery--success----------");
                                        console.log(res);
                                        that.data.timeoutNumber = setTimeout(() => {
                                            if (that.data.scanDevices.length == 0) {
                                                that.setData({
                                                    scanTimeout: true,
                                                })

                                            } else {
                                                that.setData({
                                                    scanDevices: that.data.scanDevices,
                                                })
                                            }
                                            wx.stopPullDownRefresh();
                                            that.setData({
                                                isPullingDown: false,
                                            })
                                        }, 6000);
                                        wx.onBluetoothDeviceFound(function (obj) {
                                            // console.log(obj.deviceId);
                                            var temp = that.data.scanDevices                                            
                                            var needResort = false;
                                            for(var indexx = 0; indexx < obj.devices.length; indexx ++){
                                                var dev = obj.devices[indexx];
                                                if (!dev.name || dev.name == ""){
                                                    continue;
                                                }                                                
                                                if(dev.advertisServiceUUIDs.indexOf("656C6248-0200-3263-696E-6F7274796177") < 0){
                                                    continue;
                                                }
                                                let pDev = temp.findIndex((it) => {
                                                    return it.deviceId === dev.deviceId
                                                })
                                                if (pDev == -1) {
                                                    if(dev.RSSI == 127){
                                                        continue;
                                                    }
                                                    var userIndex = wx.getStorageSync("userSetLanguage" + dev.deviceId);
                                                    if(userIndex !== "" && userIndex !== null){
                                                        dev.lastConnect = 1;
                                                    }
                                                    dev.scanTime = new Date().getTime();
                                                    temp.push(dev)
                                                    needResort = true;                                                    
                                                    console.log(dev);
                                                } else {
                                                    if(temp[pDev].RSSI != dev.RSSI){
                                                        needResort = true;
                                                    }
                                                    if(dev.RSSI == 127){
                                                        temp.slice(pDev,1);
                                                        continue;
                                                    }
                                                    // dev.RSSI = parseInt(((temp[pDev].RSSI + dev.RSSI)/2));
                                                    dev.scanTime = new Date().getTime();
                                                    dev.lastConnect = temp[pDev].lastConnect;
                                                    temp[pDev] = dev;
                                                    // temp.slice(pDev,1,dev);
                                                }
                                            }
                                            if(needResort){
                                                temp = temp.sort(function (a, b) {
                                                    return a.RSSI > b.RSSI ? -1 : 1;
                                                });
                                                let nowDateTime = (new Date()).getTime();
                                                if(that.data.lastRefreshTimestamp == 0 || (nowDateTime - that.data.lastRefreshTimestamp) > 1500){
                                                    that.data.lastRefreshTimestamp = nowDateTime;
                                                    that.setData({
                                                        scanDevices: temp
                                                    })
                                                }
                                                
                                            }
                                            
                                        })
                                    },
                                    fail: function (res) {
                                        // fail
                                        console.log("蓝牙扫描失败");
                                        console.log(res);
                                        wx.stopPullDownRefresh();
                                        that.setData({
                                            isPullingDown: false,
                                        })
                                    },
                                    complete: function (res) {
                                        // complete
                                        console.log(res);

                                    }
                                })
                            },
                            fail: function (res) {

                                if (wx.getStorageSync("language") == "zh_CN") {
                                    wx.showModal({
                                        title: app.globalData.i18n["Tips"],
                                        content: app.globalData.i18n["bt.blecheck"],
                                        showCancel: false
                                    })
                                } else {
                                    wx.showModal({
                                        title: 'Tips',
                                        content: app.globalData.i18n["bt.blecheck"],
                                        showCancel: false,
                                        confirmText: "OK"
                                    })
                                }
                                wx.stopPullDownRefresh();
                                that.setData({
                                    isPullingDown: false,
                                })
                            },
                        })
                    },
                })
            },
        })

    },
    bleClick: function (e) {
        var _this = this
        var devId = e.currentTarget.dataset.dev.deviceId;
        var devName = e.currentTarget.dataset.dev.name;
        console.log(_this.data.scanDevices)
        wx.stopPullDownRefresh({
          complete: (res) => {},
        })
        wx.stopBluetoothDevicesDiscovery({
            success: function (res) {
                console.log(res)
                // wx.showToast({
                //     title: '连接中',
                //     icon: 'loading',
                //     duration: 2000
                // })
                console.log('开始连接')
                //wx.setStorageSync('devId', devId)
                app.globalData.devId = devId
                app.globalData.devName = devName;
                // wx.switchTab({
                //     url: '../control/control'
                // })
                // user.connectBle(devId)
                wx.switchTab({
                    url: '../control/control'
                })
            }
        })
    },

    char2buf(str) {
        var out = new ArrayBuffer(str.length)
        var u8a = new Uint8Array(out)
        var strs = str.split("")
        for (var i = 0; i < strs.length; i++) {
            u8a[i] = strs[i].charCodeAt()
        }
        return out
    },

    ab2str: function (buf) {
        return String.fromCharCode.apply(null, new Uint8Array(buf));
    },

    // 字符串转为ArrayBuffer对象，参数为字符串
    str2ab: function (str) {
        var buf = new ArrayBuffer(str.length * 2); // 每个字符占用2个字节
        var bufView = new Uint16Array(buf);
        for (var i = 0, strLen = str.length; i < strLen; i++) {
            bufView[i] = str.charCodeAt(i);
        }
        return buf;
    },

    senddat: function () {
        // var serviceId = "77617974-726F-6E69-6332-000248626C65"
        // var charaIdWrite = "00010203-0405-0607-0809-0A0B0C0D2B19"
        // this.writeData(sendId, serviceId, charaIdWrite)
        wx.switchTab({
            url: '../control/control'
        })
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        if(getCurrentPages().reverse()[0].route == "pages/bt/bt"){
            wx.getBluetoothAdapterState({
                success:(res)=>{
                    if(!res.discovering){
                        this.refreshDeviceList();
                    }
                },
                fail:(res)=>{

                }
            });            
        }
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {
        this.setData({
            scanDevices:[],
        })
    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
        this.data.lastRefreshTimestamp = 0;        
        wx.stopPullDownRefresh({
          success: (res) => {
            this.refreshDeviceList();
          },
        })
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})