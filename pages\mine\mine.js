// pages/mine/mine.js
var user = require('../../utils/ble.js');
const app = getApp();
// var util = require('../../utils/util.js');
// const langData = require('../../langData.js');
// //读取默认语言
// const lg = wx.getStorageSync('language');
// const i18n = wx.getStorageSync('i18n');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        userInfo: {
            avatarUrl: "",
            nickName: "",            
        },
        connectset: "连接设置",
        devicerelate: "设备关联",
        help: "使用帮助",
        parameterset: "参数设置",
        aboutus: "关于我们",
        devshow: "开发说明：点击显示设备信息",
        devshow1: "开发说明：点击显示相关说明书",
        i18n:[],
    },

    getUserInfo: function () {
        var that = this;
        console.log("getUserInfo")
        /**
         * 获取用户信息
         */
        wx.getUserInfo({
            success: function (res) {
                console.log(res);
                var avatarUrl = 'userInfo.avatarUrl';
                var nickName = 'userInfo.nickName';
                that.setData({
                    [avatarUrl]: res.userInfo.avatarUrl,
                    [nickName]: res.userInfo.nickName,
                })
                wx.setStorage({
                  data: res.userInfo,
                  key: 'userInfo',
                });
            }
        })
    },

    conset: function () {
        wx.navigateTo({
            url: '../conset/conset'
        })
    },

    devicerelated: function () {
        wx.navigateTo({
            url: '../devicerelated/devicerelated'
        })
    },

    help: function () {
        wx.navigateTo({
            url: '../help/help'
        })
    },

    parameterset: function () {
        wx.navigateTo({
            url: '../set/set'
        })
    },

    aboutus: function () {
        wx.navigateTo({
            url: '../about/about'
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      // 代理小程序的 setData，在更新数据后，翻译传参类型的字符串
      var that = this;
      var userInfo = wx.getStorageSync("userInfo")
      //util.resetSetData.call(this, langData);
      this.setData({
        i18n: getApp().globalData.i18n,
        userInfo:userInfo
      });
      wx.setTabBarItem({
        index: 2,
        text: this.data.i18n["navBar.Me"],
      });

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    disconnect: function () {
      this.timeSet();
      if(__wxConfig.envVersion != 'release'){
        //测试时,清空配置信息;
        wx.setStorageSync('config', []);        
      }
      app.globalData.config = null;
      app.globalData.devName = "";
      wx.reLaunch({
        url: '../index/index',
        complete: function (res) {
          wx.closeBLEConnection({
            deviceId: getApp().globalData.devId,
          });
        },
      });
    },
  timeSet: function () {
    // var buffer = [0xc2, 0x03, 0x00, 0xc5];
    // user.writeData(buffer)   
  },
})