import CryptoJS from 'crypto-js';
import {
  Base64
} from 'js-base64';
let minSampleRate = 16000; //22050
var ttsCallBack = null;
var wxttsPlugin = requirePlugin("WechatSI")
function getWebsocketUrl(config) {

  switch (config.code) {
    case 'xunfei':
      {
        return new Promise((resolve, reject) => {
          var apiKey = config.key;
          var apiSecret = config.secret;
          var url = config.url; //'wss://tts-api.xfyun.cn/v2/tts'
          var host = 'tts-api.xfyun.cn'; //location.host
          var date = new Date().toGMTString()
          var algorithm = 'hmac-sha256'
          var headers = 'host date request-line'
          var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`
          var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret)
          var signature = CryptoJS.enc.Base64.stringify(signatureSha)
          var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`
          var authorization = Base64.btoa(authorizationOrigin)
          url = `${url}?authorization=${authorization}&date=${date}&host=${host}`
          resolve(url)
        })
      }
  }
}

function getAudio(config) {
  switch (config.code) {
    case 'WechatSI':{
      // let manager = wxttsPlugin.getRecordRecognitionManager()
      wxttsPlugin.textToSpeech({
        lang: config.lang,
        content:config.text,
        success:function(res){
          console.log(res);
          ttsCallBack && ttsCallBack.success && ttsCallBack.success({
            code: config.code,
            url: res.filename,
            ext:"mp3",
          });
        },
        fail:function(res){
          console.log(res);
          wx.hideLoading();
          ttsCallBack && ttsCallBack.fail && ttsCallBack.fail({
            code:config.code,
            errMsg:res.errMsg,
          });
        }
      });
      break;
    }
    case 'aliyun':{
      getToken(config,{
        success:function(res){
          console.log(res);
          var url = config.url + "?appkey=" + config.appid + "&token=" + res.token + "&format=mp3&sample_rate=16000&voice=" + config.person + "&volume=" + config.volume + "&speech_rate=" + config.speed + "&pitch_rate=" + config.pitch + "&text=" + encodeURI(config.text);
          ttsCallBack && ttsCallBack.success && ttsCallBack.success({
            code: config.code,
            url: url,
            ext: "mp3",
          })
        },
        fail:function(res){
          console.log(res);
          ttsCallBack && ttsCallBack.fail && ttsCallBack.fail({
            code:config.code,
            errMsg:res.errMsg,
          });
        }
      })
      break;
    }
    case 'baidu':{
      getToken(config,{
        success:function(res){
          console.log(res);
          var url = config.url + "?tex=" + encodeURI(encodeURI(config.text)) + "&tok=" + res.token + "&cuid=" + config.appid + "&ctp=1&lan=zh" + "&vol=" + config.volume + "&pit=" + config.pitch + "&spd=" + config.speed + "&aue=3" + "&per=" + config.person;
          ttsCallBack && ttsCallBack.success && ttsCallBack.success({
            code: config.code,
            url: url,
            ext: "mp3",
          })
        },
        fail:function(res){
          ttsCallBack && ttsCallBack.fail && ttsCallBack.fail({
            code:config.code,
            errMsg:res.errMsg,
          });
        }
      });
      break;
    }
    case 'xunfei':
      {
        getWebsocketUrl(config).then((url) => {
          // https://www.xfyun.cn/doc/tts/online_tts/API.html#接口调用流程
          connectWebsocket(url, {
            success: function(res) {

            },
            fail: function(res) {
              wx.hideLoading();
              ttsCallBack && ttsCallBack.fail && ttsCallBack.fail({
                code: 'xunfei',
                errMsg: res.errMsg
              });
            }
          });
        });

        wx.onSocketOpen(function(res) {
          console.log(res);
          var params = {
            common: {
              'app_id': config.appid,
            },
            'business': {
              'ent': 'xtts', //aisound（普通效果）,intp65（中文）,intp65_en（英文）,xtts（优化效果）
              'aue': 'raw',
              'auf': 'audio/L16;rate=16000',
              'vcn': config.person,
              'speed': config.speed,
              'volume': config.volume,
              'pitch': config.pitch,
              //'bgs': 1,
              'tte': 'UTF8', //小语种文本须使用Unicode编码，且tte=Unicode。
              'reg': '0', //0自动判断处理，如果不确定将按照英文词语拼写处理（缺省）
              'ram': '0', //0不读出所有的标点符号
              'rdn': '0', //数字发音方式, 0：自动判断（默认值）
            },
            'data': {
              'status': 2, //固定为2
              'text': Base64.encode(config.text) //需进行base64编码，约2000汉字
            }
          };
          var data = JSON.stringify(params);
          wx.sendSocketMessage({
            data: data,
            success: function(res) {
              console.log(res);
            },
            fail: function(res) {
              console.log(res);
              wx.hideLoading();
              ttsCallBack && ttsCallBack.fail && ttsCallBack.fail({
                code: 'xunfei',
                errMsg: res.errMsg
              });
              ttsCallBack = null;
            },
          });

        });
        break;
      }
    case "sibici":
      {
        /*
        var url = config.url;
        url = url + "productId=" + config.appid + "&apikey=" + config.secret + config.key;
        var requestId = generateUUID();
        var params = {
          'context': {
            'productId': config.appid,
          },
          'request': {
            'requestId': requestId,
            'audio': {
              'audioType': 'mp3', //wav
              'sampleBytes': 2,
              'sampleRate': 16000,
              'channel': 1,
            },
            'tts': {
              'text': config.text,
              'textType': 'text',
              'voiceId': config.person,
              // "language": config.lang,
              'speed': config.speed,
              'volume': config.volume,
            }
          }
        };
        var data = JSON.stringify(params);
        wx.request({
          url: url,
          method:'post',
          header:{
            'Content-Type': 'application/json',
          },
          data: data,
          success:function(res){
            console.log(res);
            wx.hideLoading();
          },
          fail:function(res){
            wx.hideLoading();
            console.log(res);
            
          },
        })
        
        break;*/
        var params = {'text': config.text,'voiceId': config.person,'speed': config.speed,'volume': config.volume,};
        wx.request({
          url: config.url,
          method:'POST',
          data: JSON.stringify(params),
          success:function(res){
            console.log(res);
            ttsCallBack && ttsCallBack.success && ttsCallBack.success({
              code: config.code,
              url:res.data.downloadUrl,
              ext: "mp3",
            })
          },
          fail:function(res){
            wx.hideLoading();
            console.log(res);
            ttsCallBack && ttsCallBack.fail && ttsCallBack.fail({
              code:config.code,
              errMsg:res.errMsg,
            });
          },        
        })
  
        break;
      }
  }

}
function configHttp(callBack){
  ttsCallBack = callBack;
}
function configWebsocket(callBack) {
  if (callBack.code != "xunfei") {
    return;
  }  
  ttsCallBack = callBack;
  var pcmBase64String = "";
  wx.onSocketMessage(function(res) {
    var obj = JSON.parse(res.data);
    // 合成失败
    if (obj.code != 0) {
      console.log(`${obj.code}:${obj.message}`);
      return;
    }
    if (obj.data.status != 2) {
      pcmBase64String = pcmBase64String + Base64.atob(obj.data.audio);
      return;
    } else {
      pcmBase64String = pcmBase64String + Base64.atob(obj.data.audio);
    }
    //转换Base64的音频字符串为音频文件；
    let sampleRate = 16000;
    let pcmData = strToUint8Array(pcmBase64String);
    ttsCallBack && ttsCallBack.success && ttsCallBack.success({
      code: 'xunfei',
      state: obj.data.status,
      audio: pcmData
    }); //当前音频流状态，0表示开始合成，1表示合成中，2表示合成结束    
    if (obj.code === 0 && obj.data.status === 2) {
      wx.closeSocket({
        reason: "合成完成",
        success: function(res) {

        },
        fail: function(res) {

        }
      })
    }
    ttsCallBack = null;
    return;

  });
  wx.onSocketError(function(res) {
    console.log(res.errMsg);
    ttsCallBack && ttsCallBack.fail && ttsCallBack.fail({
      code: 'xunfei',
      errMsg: res.errMsg
    });
    ttsCallBack = null;
  });
  wx.onSocketClose(function(res) {
    console.log(res);
  });
}

function connectWebsocket(url, callBack) {
  console.log(url);

  wx.connectSocket({
    url: encodeURI(url),
    success: function(res) {
      console.log(res);
      callBack.success(res);
    },
    fail: function(res) {
      console.log(res);
      callBack.fail(res);
    },
  });
}

function transData(audioDataStr, sampleRate) {
  let newAudioData
  let audioData = toFloat32(strToUint8Array(audioDataStr))
  if (sampleRate >= minSampleRate) {
    newAudioData = audioData
  } else {
    newAudioData = changeSampleRate(audioData, sampleRate, minSampleRate)
  }
  return newAudioData
}
//只适用于s16le的pcm；s是有符合，u无符号，16位，le是小端，be是大端；
function changeSampleRate(buffer, from) {
  var data = new Float32Array(buffer)
  var fitCount = Math.round(data.length * (minSampleRate / from))
  var newData = new Float32Array(fitCount)
  var springFactor = (data.length - 1) / (fitCount - 1)
  newData[0] = data[0]
  for (var i = 1; i < fitCount - 1; i++) {
    var tmp = i * springFactor
    var before = Math.floor(tmp).toFixed()
    var after = Math.ceil(tmp).toFixed()
    var atPoint = tmp - before
    newData[i] = data[before] + (data[after] - data[before]) * atPoint
  }
  newData[fitCount - 1] = data[data.length - 1]
  return newData
}

function toFloat32(uint8Array) {
  var tmp = new Int16Array(new DataView(uint8Array.buffer).buffer)
  var tmpData = []
  for (let i = 0; i < tmp.length; i++) {
    var d = tmp[i] < 0 ? tmp[i] / 0x8000 : tmp[i] / 0x7FFF
    tmpData.push(d)
  }
  return new Float32Array(tmpData)
}

function strToUint8Array(audioRawDataStr) {
  const outputArray = new Uint8Array(audioRawDataStr.length)
  for (let i = 0; i < audioRawDataStr.length; ++i) {
    outputArray[i] = audioRawDataStr.charCodeAt(i)
  }
  return outputArray
}

function encodeWAV(bytes, sampleRate) {
  // var sampleRate = 16000;
  var sampleBits = 16;
  // var bytes = encodePCM();
  var buffer = new ArrayBuffer(44 + bytes.byteLength);
  var data = new DataView(buffer);

  var channelCount = 1; // 单声道
  var offset = 0;

  // 资源交换文件标识符 
  writeString(data, offset, 'RIFF');
  offset += 4;
  // 下个地址开始到文件尾总字节数,即文件大小-8 
  data.setUint32(offset, 36 + bytes.byteLength, true);
  offset += 4;
  // WAV文件标志
  writeString(data, offset, 'WAVE');
  offset += 4;
  // 波形格式标志 
  writeString(data, offset, 'fmt ');
  offset += 4;
  // 过滤字节,一般为 0x10 = 16 
  data.setUint32(offset, 16, true);
  offset += 4;
  // 格式类别 (PCM形式采样数据) 
  data.setUint16(offset, 1, true);
  offset += 2;
  // 通道数 
  data.setUint16(offset, channelCount, true);
  offset += 2;
  // 采样率,每秒样本数,表示每个通道的播放速度 
  data.setUint32(offset, sampleRate, true);
  offset += 4;
  // 波形数据传输率 (每秒平均字节数) 单声道×每秒数据位数×每样本数据位/8 
  data.setUint32(offset, channelCount * sampleRate * (sampleBits / 8), true);
  offset += 4;
  // 快数据调整数 采样一次占用字节数 单声道×每样本的数据位数/8 
  data.setUint16(offset, channelCount * (sampleBits / 8), true);
  offset += 2;
  // 每样本数据位数 
  data.setUint16(offset, sampleBits, true);
  offset += 2;
  // 数据标识符 
  writeString(data, offset, 'data');
  offset += 4;
  // 采样数据总数,即数据总大小-44 
  data.setUint32(offset, bytes.byteLength, true);
  offset += 4;

  // 给wav头增加pcm体
  for (let i = 0; i < bytes.byteLength; ++i) {
    data.setUint8(offset, bytes.getUint8(i, true), true);
    offset++;
  }

  return data;
}

function writeString(data, offset, str) {
  for (var i = 0; i < str.length; i++) {
    data.setUint8(offset + i, str.charCodeAt(i));
  }
}

function newNameOfDuplicatFile(fileList, elementname) {
  var index = 0;
  var lastPoint = elementname.lastIndexOf(".");
  var name = elementname.substr(0, lastPoint);
  var ext = elementname.substr(lastPoint + 1, 5);
  if(name.length >=24){
    name = name.substr(0,24);
  }
  var newname = name;
  while (fileList.indexOf(newname + "." + ext) >= 0) {
    index++;
    newname = name + "-" + index;
  }
  return newname + "." + ext;
}

function generateUUID() {
  var d = new Date().getTime();
  // if (window.performance && typeof window.performance.now === "function") {
  //   d += performance.now(); //use high-precision timer if available
  // }
  var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
  });
  return uuid;
}
function getUTCDateString(){
  var date = new Date()
  // 获取年：
  var y =  date.getUTCFullYear();    
  // 获取月： 
  var m = date.getUTCMonth() ;
  // 获取日： 
  var d = date.getUTCDate();
  // 获取小时：
  var h= date.getUTCHours();
  // 获取分钟：
  var M = date.getUTCMinutes();
  // 获取秒钟：
  var s = date.getUTCSeconds();

  var utc = Date.UTC(y,m,d,h,M,s);
  // var utc = ISODateString(new Date());
  return utc;
}

function getToken(config,callBack){
  switch (config.code){
    case "baidu":{
      var lastBaiduToken = wx.getStorageSync("baiduToken");
      if (lastBaiduToken && lastBaiduToken.time - 60 >= (new Date()).getTime()) {
        var token = lastBaiduToken.token;
        callBack.success({
          token:token,
        })
      } else {
        var tokenUrl = config.tokenUrl;
        tokenUrl = tokenUrl + "?grant_type=client_credentials&client_id=" + config.key + "&client_secret=" + config.secret;
        wx.request({
          url: tokenUrl,
          success: function (res) {
            console.log(res.data.access_token);
            wx.setStorageSync("baiduToken", { time: res.data.expires_in + (new Date()).getTime(), token: res.data.access_token });
            callBack.success({
              token: res.data.access_token,
            })
          },
          fail: function (res) {
            callBack.fail(res);
          }
        })
      }
      break;
    }
    case "aliyun":{
      var lastAliyunToken = wx.getStorageSync("aliyunToken");
      if (lastAliyunToken && lastAliyunToken.time - 60 * 5  >= (new Date()).getTime() / 1000) {
        var token = lastAliyunToken.token;
        callBack.success({
          token:token,
        })
      } else {

        var timestamp = encodeURIComponent(ISODateString(new Date()));
        var signatureNonce = generateUUID();       

        var queryString = "AccessKeyId=" + config.key + "&Action=CreateToken&Format=JSON&RegionId=cn-shanghai&SignatureMethod=HMAC-SHA1&SignatureNonce=" + signatureNonce + "&SignatureVersion=1.0&Timestamp=" + timestamp + "&Version=2019-02-28"

        var queryStringPencent = "GET&%2F&" + encodeURIComponent(queryString);
       
        var signatureSha = CryptoJS.HmacSHA1(queryStringPencent, config.secret + "&");
        var signature = encodeURI(CryptoJS.enc.Base64.stringify(signatureSha)).replace("+","%2B").replace("*","%2A").replace("%7E", "~").replace("=", "%3D").replace("+","%2B").replace("*","%2A").replace("%7E", "~").replace("=", "%3D").replace("+","%2B").replace("*","%2A").replace("%7E", "~").replace("=", "%3D");
        var queryStringWithSign = "?Signature=" + signature + "&" + queryString;

        var tokenUrl = config.tokenUrl + queryStringWithSign;
        console.log(tokenUrl);

        wx.request({
          url: tokenUrl,
          success: function (res) {
            console.log(res.data.Token);
            if(res.data.Token && res.data.Token.ExpireTime){
              wx.setStorageSync("aliyunToken", { time: res.data.Token.ExpireTime , token: res.data.Token.Id });
              callBack.success({
                token: res.data.Token.Id,
              })
            }else{
              callBack.fail(res);
            }            
          },
          fail: function (res) {
            callBack.fail(res);
          },complete:function(){
            console.log("怎么了");
          }
        })
      }
      break;
    }
    default:{
      callBack.fail({errMsg:"invalid config"});
    }
  }
}
function getISO8601DateStr2Date(string)
{
    var regexp = "([0-9]{4})(-([0-9]{2})(-([0-9]{2})" +
        "(T([0-9]{2}):([0-9]{2})(:([0-9]{2})(\.([0-9]+))?)?" +
        "(Z|(([-+])([0-9]{2}):([0-9]{2})))?)?)?)?";
    if(string)
    {
        var d = string.match(new RegExp(regexp));
        var offset = 0;
        var date = new Date(d[1], 0, 1);
 
        if (d[3]) {
            date.setMonth(d[3] - 1);
        }
        if (d[5]) {
            date.setDate(d[5]);
        }
        if (d[7]) {
            date.setHours(d[7]);
        }
        if (d[8]) {
            date.setMinutes(d[8]);
        }
        if (d[10]) {
            date.setSeconds(d[10]);
        }
        if (d[12]) {
            date.setMilliseconds(Number("0." + d[12]) * 1000);
        }
        if (d[14]) {
            offset = (Number(d[16]) * 60) + Number(d[17]);
            offset *= ((d[15] == '-') ? 1 : -1);
        }
        offset -= date.getTimezoneOffset();
        var time = (Number(date) + (offset * 60 * 1000));
        return time;
    }
    else
    {
        return null;
    }
}

function ISODateString(d) {
  function pad(n){
      return n<10 ? '0'+n : n
  }
  return d.getUTCFullYear()+'-'
  + pad(d.getUTCMonth()+1)+'-'
  + pad(d.getUTCDate())+'T'
  + pad(d.getUTCHours())+':'
  + pad(d.getUTCMinutes())+':'
  + pad(d.getUTCSeconds())+'Z'
}
export default {
  // getWebsocketUrl,
  configWebsocket,
  configHttp,
  getAudio,
  encodeWAV,
  newNameOfDuplicatFile,
  getISO8601DateStr2Date,
}