// pages/password/password.js
const app = getApp()
var user = require('../../utils/ble.js');
var time = 0;
Page({

    /**
     * 页面的初始数据
     */
    data: {
        blename: "确定",
        oldtext: "请输入6位旧密码",
        newtext: "请输入6位新密码",
        inputold: "",
        inputnew: "",
        i18n:[],
        isSetpassword:false,
    },

    getInputold: function (e) {//方法1
        this.setData({
            inputold: e.detail.value
        });
    },

    getInputnew: function (e) {//方法1
        this.setData({
            inputnew: e.detail.value
        });
    },

    setname: function () {
        var that = this;

        var strold = this.data.inputold
        var strnew = this.data.inputnew
        if (strold.length != 6) {
          // if (wx.getStorageSync("language") == "zh_CN") {
          //   wx.showToast({
          //     title: app.globalData.i18n["password.oldtext"],
          //     image: '/images/error.png',
          //       duration: 1000
          //   })
          // }else{
            wx.showModal({
              title: app.globalData.i18n['Tips'],
              content: app.globalData.i18n["password.oldtext"],
              showCancel: false,
              confirmText: "OK"
            })
          // }
        }
        else if (strnew.length != 6) {
          // if (wx.getStorageSync("language") == "zh_CN") {
          //   wx.showToast({
          //     title: app.globalData.i18n["password.newtext"],
          //     image: '/images/error.png',
          //       duration: 1000
          //   })
          // }else{
            wx.showModal({
              title: app.globalData.i18n['Tips'],
              content: app.globalData.i18n["password.newtext"],
              showCancel: false,
              confirmText: "OK"
            })
          // }
        }
        else {
            var buffer = new Uint8Array(15);
            buffer[0] = 0xD2

            buffer[1] = strold.charCodeAt(0)
            buffer[2] = strold.charCodeAt(1)
            buffer[3] = strold.charCodeAt(2)
            buffer[4] = strold.charCodeAt(3)
            buffer[5] = strold.charCodeAt(4)
            buffer[6] = strold.charCodeAt(5)

            buffer[7] = strnew.charCodeAt(0)
            buffer[8] = strnew.charCodeAt(1)
            buffer[9] = strnew.charCodeAt(2)
            buffer[10] = strnew.charCodeAt(3)
            buffer[11] = strnew.charCodeAt(4)
            buffer[12] = strnew.charCodeAt(5)

            var tem = 0
            for(var i=0;i<13;i++){
                tem += buffer[i];
            }

            buffer[13] = tem >> 8
            buffer[14] = tem
            user.writeData(buffer)

            that.data.isSetpassword = true

            time = 0;
            var interval = setInterval(function () {
                if (that.data.isSetpassword) {
                    time++;
                    if (time < 3) {

                        user.writeData(buffer)
                    }
                    else {
                        that.data.isSetpassword = false;
                        clearInterval(interval);
                        wx.hideLoading();
                      if (wx.getStorageSync("language") == "zh_CN") {
                        wx.showToast({
                          title: app.globalData.i18n["password.olderror"],
                          image: '/images/error.png', 
                            duration: 1000
                        })
                      }else{
                        wx.showModal({
                          title: app.globalData.i18n["Tips"],
                          content: app.globalData.i18n["password.olderror"],
                          showCancel: false,
                          confirmText: "OK"
                        })
                      }
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
                title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }

    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this
        this.setData({
          i18n: getApp().globalData.i18n
        });
        user.passwordCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据b', user.hexstringFromBuffer(buf))

            if (that.data.isSetpassword) {
                console.log("isSetname")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xBF) && (bufView[7] == 0x7A)) {
                    that.data.isSetpassword = false

                    wx.hideLoading();

                    if (bufView[4] == 0x01){
                        wx.showToast({
                          title: that.data.i18n["password.updateok"],
                            duration: 1000
                        })
                    } else if (bufView[4] == 0x00) {
                      if (wx.getStorageSync("language") == "zh_CN") {
                        wx.showToast({
                          title: app.globalData.i18n["password.olderror"],
                          image: '/images/error.png',
                          duration: 1000
                        })
                      } else {
                        wx.showModal({
                          title: app.globalData.i18n["Tips"],
                          content: app.globalData.i18n["password.olderror"],
                          showCancel: false,
                          confirmText: "OK"
                        })
                      }
                    }

                    
                }
            }

        }    
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.passwordCallback = null;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})