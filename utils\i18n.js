//中英文切换模板

var i18n = {};
i18n.en = {
  "Tips": "Tips",
  "cancel":"Cancel",
  "confirm":"OK",
  "navBar.Timing":"Timing",
  "navBar.Me": "Mine",
  "navBar.Control": "Control",
  "mine.aboutus":"About Us",
  "mine.disconnect":"Disconnect",
  "mine.settings": "Settings",
  "mine.help": "Help",
  "mine.device": "Device",
  "mine.help_detail": "Display related instructions",
  "mine.device_detail": "Display the device infomation",
  "mine.userInfo": "Display the user infomation",
  "set.languagesetting":"Language for broadcast",
  "set.name":"Bluetooth name",
  "set.password":"Change password",
  "set.battery":"Battery",
  "blename.confirm":"OK",
  "blename.bletext": "Please enter a new bluetooth name",
  "blename.tips.name": "Set name error!",
  "blename.tips.nameok":"Set name OK!",
  "timer.timingPlay":"Timing Play",
  "timer.interPlay":"Interval Play",
  "timer.dataSync":"Data Synchronize",
  "timer.repeat":"Repeat",
  "timer.datanum": "Mon Wed",
  "timer.songnum": "Song name",
  "timer.controlnum": "Singel",
  "timer.volumenum": "Volume 40",
  "timer.relaynum": "Relay",
  "timer.setsynplay": "Setup Synchronize",
  "timer.getdoc": "Get document",
  "timer.storedata": "Data store",
  "timer.deleteall": "Delete all",
  "timer.volume": "Volume",
  "timer.tips.nodata":"Data is empty",
  "timer.loostr.Singlenoloop": "Single no loop",
  "timer.loostr.Singleloop": "Single loop",
  "timer.loostr.Allnoloop": "All no loop",
  "timer.loostr.Allloop": "All loop",
  "timer.relaystr.on": "On",
  "timer.relaystr.off": "Off",
  "timer.weekstr.0": "Sun，",
  "timer.weekstr.1": "Mon，",
  "timer.weekstr.2": "Tue，",
  "timer.weekstr.3": "Wen，",
  "timer.weekstr.4": "Thu，",
  "timer.weekstr.5": "Fri，",
  "timer.weekstr.6": "Sat，",
  "timer.edit":"Edit",
  "timer.Interval": "Interval",
  "timer.min": "Min",
  "timer.deleteallok": "Delete all OK",
  "timer.tips.storeok":"Save data OK",
  "timer.tips.fetchstoreok":"Fetch data OK",
  "password.confirm": "OK",
  "password.oldtext": "Please input an old 6-digit password",
  "password.newtext": "Please input a new 6-digit password",
  "password.updateok": "Update ok",
  "password.olderror": "password error",
  "help.help1": "WeChat applet",
  "help.help2": "蓝牙配对",
  "help.help3": "语音更换",
  "help.help4": "语音文件删除",
  "help.help5": "语音文件制作指导",
  "help.help6": "音频输出",
  "help.help7": "音量调节",
  "help.help8": "触发方式",
  "help.help9": "供电方式",
  "timeplay.week.0": "Sun",
  "timeplay.week.1": "Mon",
  "timeplay.week.2": "Tue",
  "timeplay.week.3": "Wen",
  "timeplay.week.4": "Thu",
  "timeplay.week.5": "Fri",
  "timeplay.week.6": "Sat",
  "timeplay.cancel": "Cancel",
  "timeplay.enter": "OK",
  "timeplay.text1": "Repeat",
  "timeplay.text2": "Sun，Mon，Tue，Wed，Thu，Fri，Sat",
  "timeplay.text3": "Audio name",
  "timeplay.text4": "",
  "timeplay.text5": "Volume",
  "timeplay.text6": "Relay",
  "timeplay.text7": "Start time",
  "timeplay.text9": "End Time",
  "timeplay.tips.repeat":"Please input the repeat of week",
  "timeplay.tips.song": "Please input the audio setting",
  "timeplay.tips.settingok": "Setting OK",
  "timeplay.tips.interval": "Time interval is too small",
  "timeplay.tips.date":"Please input the repeat date",
  "intervalplay.interval": "Play Interval(Min)",
  "intervalplay.tips.interval": "Time Interval 1-255",
  "voicename.Optionaltracks": "Custom",
  "control.inductionnum": "Sensor Switch",
  "control.songnum": "Audio Name",
  "control.trigger": "Trigger Mode",
  "control.playmode": "Play Mode",
  "control.record": "Record",
  "control.voice": "Device's Audio",
  "control.doc": "Change Audio",
  "control.customize": "Customize",
  "control.recording": "Recording",
  "control.tips.password": "Please enter password",
  "control.tips.disconnected": "Disconnected",
  "control.tips.passworderror": "Wrong password!",
  "control.tips.recordfailed": "Failed to start recording",
  "control.tips.recordstopfailed": "Stop recording failed",
  "control.tips.recordstoped": "Record stoped",
  "control.tips.loginerror": "Login error!",
  "control.tips.adjustdateerror": "Adjust date error!",
  "control.tips.adjustingdate": "Adjusting date",  
  "control.tips.connecting":"Connecting...",
  "control.input.password":"Bluetooth Pair Code",
  "control.input.connect":"Connect",
  "control.tips.loading":"Loading...",
  "control.tips.sending": "Sending...",
  "control.tips.sendingFailed": "Send command failed",
  "control.tips.tips": "Tips",
  "control.tips.initialfailed": "Initialization failed",
  "control.tips.retry": "Retry", 
  "trigger.text1": "Infrared body induction trigger",
  "trigger.text2": "Magnetic induction trigger",
  "trigger.text3": "Microwave induction trigger",
  "trigger.text4": "Proximity sensing trigger",
  "trigger.text5": "External input trigger",
  "trigger.text6": "Power on Play",
  "trigger.text7": "Relay output control 1",
  "trigger.text8": "Synchronize Settings",
  "trigger.synok":"Synchronized",
  "trigger.error":"trigger error!",
  "play.text1": "Trigger single playback",
  "play.text2": "Trigger all playback",
  "play.text3": "Single automatic loop playback",
  "play.text5": "Alarm status single loop playback",
  "play.text4": "All automatic loop playback",
  "play.text6": "Alarm status all loop playback",
  "play.text7": "Relay output control 1",
  "play.text8": "Synchronize Settings",
  "voicemanager.lastsong": "Delete last",
  "voicemanager.allsong": "Delete All",
  "voicemanager.deleteok":"Delete success!",
  "voicemanager.playing":"Playing...",
  "voicemanager.deleting": "Deleting...",
  "voicemanager.deleteerror": "Delall error!",
  "voicemanager.voicerror": "voice error!",
  "voicemanager.playerror": "play error!",
  "SendOK": "Send OK",
  "voicechange.import":"Import Audio",
  "customize.datetext": "Date",
  "customize.timetext": "Relay time",
  "customize.sendtext": "Send",
  "customize.number": "The number must be between 3 and 99. ",
  "customize.key4": " Please enter 4 digits Key",
  "customize.location":"Location",
  "customize.getlocation":"Get Location",
  "customize.LocationSetting":"Location Setting",
  "customize.getlocationFail":"Fail to Change Location",
  "customize.coordinate":"Coordinate",
  "bt.borded": "Paired Devices",
  "bt.unborded": "Devices",
  "bt.blecheck": "Please check if Bluetooth is on",
  "bt.notfoundTitle":"Device not found!",
  "bt.notfoundGPS":"1. Please check whether the GPS switch has been turned on ",
  "bt.notfoundBLE":"2. Please check whether the Bluetooth switch has been turned on",
  "bt.notfoundRelaunch":"3. Please relaunch the application to scan.",
  "index.search": "Search for devices",
  "index.action": "Attentions",
  "index.action1": "1.Before searching for the device, please turn on the phone Bluetooth.",
  "index.action2": "2.When the device is not found, make sure the device is powered.",
  "index.action3": "3.Do not exit the configuration process abnormally when initializing the device.",
  "components.delete":"Delete",
  "set.tips.batter":"Batterry error!",
  "record.cancel":"Cancel",
  "record.soLessTime":"Too short\r\nto record",
  "record.error":"Speech error",
  "record.recording":"Recording...",
  "record.saveing": "Saving...",
  "lanugage.updateok":"Setting OK",
  "language.tips.settingerror":"Setting fail",
  "timing.tips.disable":"Timing setting is disable.",
  "localaudio.tips.newname":"Please input the new file name.",
  "localaudio.tips.fileexists":"File already exists",
  "localaudio.text.import":"Import",
  "localaudio.text.tts":"TTS",
  "localaudio.text.recorder":"Recorder",
  "localaudio.text.send":"Send",
  "localaudio.text.clear":"Clear",
  "localaudio.text.delete":"Delete",
  "localaudio.text.rename":"Rename",
  "localaudio.text.inputVoice":"Record",
  "localaudio.tip.maxsizelimted":"The maximum size of the file storage limit (10M) is exceeded",
  'localaudio.tip.neterr':"exception onOpen fail code:9, msg:Timer Expired",
  "tts.tip.inputpls":"Please enter text",
  "tts.txt.engine":"TTS Engine",
  "tts.txt.speaker":"Speaker",
  "tts.txt.volume":"Volume",
  "tts.txt.speed":"Speed",
  "tts.txt.pitch":"Pitch",
  "tts.txt.ttsplay":"TTS and Play",
  "tts.txt.txtcount":"Character count",
  "tts.tip.copy":"Press and hold the url for a while to copy it  and then paste to the browser for donwloading.",
  'tts.err.tts':"TTS error",
  'tts.tip.copyok':"Copy success",
  "lastConnected":"Last connected",
  "offline":"Offline",
  "online":"Online",
}
i18n.zh_CN = {
  "cancel": "取消",
  "confirm": "确定",
  "navBar.Timing": "定时",
  "navBar.Me": "我的",
  "navBar.Control": "控制",
  "mine.aboutus":"关于我们",
  "mine.disconnect":"断开连接",
  "mine.settings":"参数设置",
  "mine.help": "使用帮助",
  "mine.device": "设备关联",
  "mine.help_detail":"开发说明：点击显示相关说明书",
  "mine.device_detail":"开发说明：点击显示设备信息",
  "mine.userInfo": "显示用户信息",
  "set.languagesetting":"播报的语言",
  "set.name": "蓝牙名称",
  "set.password": "密码修改",
  "set.battery": "电池电量",
  "blename.confirm": "确定",
  "blename.bletext": "请输入新的蓝牙名称",
  "blename.tips.name":"修改名称失败",
  "blename.tips.nameok": "修改名称成功",
  "timer.timingPlay": "定时播放",
  "timer.interPlay": "间隔播放",
  "timer.dataSync": "设置同步",
  "timer.repeat": "重复",
  "timer.datanum": "周一 周三",
  "timer.songnum": "歌曲名",
  "timer.controlnum": "单次",
  "timer.volumenum": "音量 40",
  "timer.relaynum": "继电器",
  "timer.setsynplay": "设置同步",
  "timer.getdoc": "获取存挡",
  "timer.storedata": "数据存储",
  "timer.deleteall": "批量删除",
  "timer.deleteallok": "批量删除成功",
  "timer.volume":"音量",
  "timer.loostr.Singlenoloop":"单曲不循环",
  "timer.loostr.Singleloop": "单曲循环",
  "timer.loostr.Allnoloop": "全部不循环",
  "timer.loostr.Allloop": "全部循环",
  "timer.relaystr.on":"开",
  "timer.relaystr.off":"无",
  "timer.weekstr.0":"周日，",
  "timer.weekstr.1": "周一，",
  "timer.weekstr.2": "周二，",
  "timer.weekstr.3": "周三，",
  "timer.weekstr.4": "周四，",
  "timer.weekstr.5": "周五，",
  "timer.weekstr.6": "周六，",
  "timer.edit": "编辑",
  "timer.interval": "间隔",
  "timer.min": "分钟",
  "timer.tips.nodata": "数据为空",
  "timer.tips.storeok":"数据保存成功",
  "timer.tips.fetchstoreok": "存档获取成功",
  "password.confirm": "确定",
  "password.oldtext": "请输入6位旧密码",
  "password.newtext": "请输入6位新密码",
  "password.updateok": "更新密码成功",
  "password.olderror": "旧密码错误",
  "help.help1": "微信小程序",
  "help.help2": "蓝牙配对",
  "help.help3": "语音更换",
  "help.help4": "语音文件删除",
  "help.help5": "语音文件制作指导",
  "help.help6": "音频输出",
  "help.help7": "音量调节",
  "help.help8": "触发方式",
  "help.help9": "供电方式",
  "timeplay.cancel": "取消",
  "timeplay.enter": "确认",
  "timeplay.text1": "重复",
  "timeplay.text2": "周日，周一，周二，周三，周四，周五，周六",
  "timeplay.text3": "选曲",
  "timeplay.text4": "",
  "timeplay.text5": "设置音量",
  "timeplay.text6": "继电器",
  "timeplay.text7": "开始时间",
  "timeplay.text9": "结束时间",
  "timeplay.week.0": "周日",
  "timeplay.week.1": "周一",
  "timeplay.week.2": "周二",
  "timeplay.week.3": "周三",
  "timeplay.week.4": "周四",
  "timeplay.week.5": "周五",
  "timeplay.week.6": "周六",
  "timeplay.tips.repeat": "请选择重复周",
  "timeplay.tips.song": "请选择歌曲",
  "timeplay.tips.settingok": "设置成功",
  "timeplay.tips.interval": "结束时间太小",
  "timeplay.tips.date": "请选择重复日期",
  "intervalplay.interval": "播放间隔（分钟）",
  "intervalplay.tips.interval":"间隔时间1-255",
  "voicename.Optionaltracks":"自选曲目",
  "control.inductionnum": "感应开关",
  "control.songnum": "歌名",
  "control.trigger": "触发方式",
  "control.playmode": "播放模式",
  "control.record": "录音更换",
  "control.voice": "设备语音",
  "control.doc": "语音更换",
  "control.customize": "自定义",
  "control.recording": "正在录音",
  "control.tips.password":"请输入6位密码",
  "control.tips.disconnected":"连接已经断开",
  "control.tips.passworderror":"密码错误!",
  "control.tips.recordfailed":"开始录音失败",
  "control.tips.recordstopfailed":"停止录音失败",
  "control.tips.recordstoped": "录音停止",
  "control.tips.loginerror":"登录失败",
  "control.tips.adjustdateerror": "时间校正失败",
  "control.tips.adjustingdate":"正在校正时间",  
  "control.tips.connecting": "正在连接...",
  "control.input.password": "蓝牙配对密码",
  "control.input.connect": "连接",
  "control.tips.loading": "正在加载...",
  "control.tips.sending": "正在发送...",
  "control.tips.sendingFailed": "发送失败",
  "Tips":"提示",
  "control.tips.initialfailed":"初始化失败",
  "control.tips.retry": "重试",
  "trigger.text1": "红外人体感应触发",
  "trigger.text2": "磁性感应触发",
  "trigger.text3": "微波感应触发",
  "trigger.text4": "近距离感应触发",
  "trigger.text5": "外部输入触发",
  "trigger.text6": "上电响",
  "trigger.text7": "继电器输出控制 1",
  "trigger.text8": "设置同步",
  "trigger.synok": "同步数据成功",
  "trigger.error": "同步失败",
  "play.text1": "触发单曲播放",
  "play.text2": "触发全曲播放",
  "play.text3": "（上电后）单曲自动循环播放",
  "play.text4": "（上电后）全曲自动循环播放",
  "play.text5": "警报状态单曲循环播放",
  "play.text6": "警报状态全曲循环播放",
  "play.text7": "继电器输出控制 1",
  "play.text8": "设置同步",
  "voicemanager.lastsong": "删除最后一首",
  "voicemanager.allsong": "全部删除",
  "voicemanager.deleteok": "删除成功",
  "voicemanager.playing": "正在播放...",
  "voicemanager.deleting": "正在删除...",
  "voicemanager.deleteerror": "删除错误!",
  "voicemanager.voicerror":"语音错误",
  "voicemanager.playerror":"播放出错",
  "SendOK":"发送成功",
  "voicechange.import": "导入文件",
  "customize.datetext": "日期",
  "customize.timetext": "继电器时间",
  "customize.sendtext": "发送",
  "customize.number":"数字必须3—99",
  "customize.key4": "请输入4位Key",
  "customize.location":"位置信息",
  "customize.getlocation":"获取定位",
  "customize.LocationSetting":"修改定位设置",
  "customize.getlocationFail":"请修改定位设置",
  "customize.coordinate":"经纬度",
  "bt.borded": "已配对的设备",
  "bt.unborded": "可用设备",
  "bt.blecheck":"请检测手机蓝牙是否打开",
  "bt.notfoundTitle":"没有找到设备!",
  "bt.notfoundGPS":"1.请检查是否已经打开手机的GPS定位开关.",
  "bt.notfoundBLE":"2.请检查是否已经打开手机的蓝牙开关.",
  "bt.notfoundRelaunch":"3.请重新打开小程序,进行搜索.",
  "index.search": "搜索设备",
  "index.action": "注意事项",
  "index.action1": "1.搜索设备前，请打开手机蓝牙",
  "index.action2": "2.找不到设备时，请确定设备已通电",
  "index.action3": "3.初始化设备时，请勿异常退出配置流程",
  "components.delete": "删除",
  "set.tips.batter": "电量读取失败!",
  "record.cancel":"取消录音",
  "record.soLessTime":"时间不足",
  "record.error":"语音错误",
  "record.recording":"录音中...",
  "record.saveing": "正在保存...",
  "lanugage.updateok":"设置成功",
  "language.tips.settingerror":"设置失败",
  "timing.tips.disable":"设备不具备定时功能.",
  "localaudio.tips.newname":"请输入新的文件名称",
  "localaudio.tips.fileexists":"文件已经存在",
  "localaudio.text.import":"导入",
  "localaudio.text.tts":"合成",
  "localaudio.text.recorder":"录音",
  "localaudio.text.send":"数传",
  "localaudio.text.clear":"清空",
  "localaudio.text.delete":"删除",
  "localaudio.text.rename":"改名",
  "localaudio.text.inputVoice":"录入",
  "localaudio.tip.maxsizelimted":"超过文件存储限制的最大大小(10M)",
  'localaudio.tip.neterr':"网络错误,请切换网络连接",
  "tts.tip.inputpls":"请输入文本",
  "tts.txt.engine":"选择平台",
  "tts.txt.speaker":"选择发音人",
  "tts.txt.volume":"选择音量",
  "tts.txt.speed":"选择语速",
  "tts.txt.pitch":"选择声调",
  "tts.txt.ttsplay":"合成并试听",
  "tts.txt.txtcount":"字数统计",
  "tts.tip.copy":"按住下载链接一段时间将其复制，然后粘贴到浏览器中进行下载",
  'tts.err.tts':"合成语言错误",
  'tts.tip.copyok':"复制成功",
  "lastConnected":"上次连接成功",
  "offline":"离线",
  "online":"在线",
}

module.exports = i18n;