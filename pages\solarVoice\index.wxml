<!--pages/forestindex/index.wxml-->
<view>
  <view class="conectstate">
    <view class="goconnect">
    <image src="../../images/forest/blelogo.png" style="width:86rpx;height: 86rpx;" mode="widthFix"></image>
      <view wx:if="{{!isconnect}}" class="goconnect-btn" bindtap="goBleConnect">
        <text>连接设备</text>
        <image src="../../images/forest/goconnect.png" mode="widthFix"></image>
      </view>
      <view wx:if="{{isconnect}}" class="connected">
        <text style="font-family: PingFangSC-medium;font-size: 32rpx;color:#FFFFFF;">太阳能语音宣传杆</text>
        <text style="font-family: PingFangSC-light;font-size: 24rpx;color:#FFFFFF;">设备型号</text>
      </view>
    </view>
    <view class="blestate">
      <view class="leftstate">
        <text wx:if="{{!isconnect}}" style="font-size:28rpx;font-family: PingFangSC-medium;">-</text>
        <view wx:if="{{isconnect}}" style="display: flex;align-items: center;justify-content: center;">
          <image src="../../images/forest/ze-checked.png" style="width:40rpx;height: 40rpx;" mode="widthFix"></image>
          <text style="margin-left: 5px;font-size:28rpx;font-family: PingFangSC-medium;">正常</text>
        </view>
        <text style="margin-top: 5px;font-size:20rpx;font-family: PingFangSC-light;">当前状态</text>
      </view>
      <view class="rightstate">
        <view class="bleconect" wx:if="{{!isconnect}}">
          <image src="../../images/forest/break.png" style="margin-right: 1px;width:40rpx;height: 40rpx;" mode="widthFix"></image>
          <text style="font-size:28rpx;font-family: PingFangSC-medium;">已断开</text>
        </view>
        <view class="bleconect" wx:if="{{isconnect}}" bindtap="disconnectble">
          <image src="../../images/forest/connectble.png" style="margin-right: 1px;width:40rpx;height: 40rpx;" mode="widthFix"></image>
          <text style="font-size:28rpx;font-family: PingFangSC-medium;">已连接</text>
        </view>
        <text style="margin-top: 5px;font-size:20rpx;font-family: PingFangSC-light;">蓝牙状态</text>
      </view>
    </view>
  </view>
  

  <view class="controlbox">
    <view class="controlbox-top">
        <view class="controlbox-top-item">
            感应开关
            <!-- <switch checked="true" color="#0C6B3A" class="controlbox-top-item-switch" bindchange="changOnOff"></switch> -->
            <view class="controlbox-top-item-switch {{isON ? 'controlbox-top-item-switch-on' : ''}}" bindtap="changOnOff"></view>
        </view>
        <view class="controlbox-top-item" bindtap="changetimePlay">
            定时播放
            <image
                class="controlbox-top-item-icon"
                src="/images/icons/alarm.svg"
                mode="scaleToFill"
            />
        </view>
    </view>

    <view class="controlbox-play">
        <view class="controlbox-play-btn">
            <image
                wx:if="{{isPlay}}"
                src="/images/icons/play.png"
                mode="scaleToFill"
            />
            <image
                wx:else
                src="/images/pause.png"
                mode="scaleToFill"
            />
        </view>
        <image
            class="controlbox-play-btn-add-icon"
            src="/images/icons/add.png"
            mode="scaleToFill"
        />
        <image
            class="controlbox-play-btn-minus-icon"
            src="/images/icons/minus.png"
            mode="scaleToFill"
        />
        <image
            class="controlbox-play-btn-next-icon"
            src="/images/icons/next.png"
            mode="scaleToFill"
        />
        <image
            class="controlbox-play-btn-prev-icon"
            src="/images/icons/prev.png"
            mode="scaleToFill"
        />
    </view>
  </view>

  <view class="controlbox-bottom">
    <view class="controlbox-bottom-item">
        <image
            class="controlbox-bottom-item-icon"
            src="/images/icons/touchType.png"
            mode="scaleToFill"
        />
        <text>触发方式</text>
    </view>
    <view class="controlbox-bottom-item">
        <image
            class="controlbox-bottom-item-icon"
            src="/images/icons/playMode.png"
            mode="scaleToFill"
        />
        <text>播放方式</text>
    </view>
    <view class="controlbox-bottom-item">
        <image
            class="controlbox-bottom-item-icon"
            src="/images/icons/recordChange.png"
            mode="scaleToFill"
        />
        <text>录音更换</text>
    </view>
    <view class="controlbox-bottom-item">
        <image
            class="controlbox-bottom-item-icon"
            src="/images/icons/deviceVoice.png"
            mode="scaleToFill"
        />
        <text>设备语音</text>
    </view>
    <view class="controlbox-bottom-item">
        <image
            class="controlbox-bottom-item-icon"
            src="/images/icons/voiceChange.png"
            mode="scaleToFill"
        />
        <text>语音更换</text>
    </view>
    <view class="controlbox-bottom-item">
        <image
            class="controlbox-bottom-item-icon"
            src="/images/icons/custom.png"
            mode="scaleToFill"
        />
        <text>自定义</text>
    </view>
  </view>


  <!-- <view class="bntbox">
    <view class="bnttype">
      <view class="bnt" bindtouchend="startDeviceRelease" bindtouchstart="startDevicePress">启动</view>
      <view class="segmentation"></view>
      <view class="bnt" bindtouchend="stopDeviceRelease" bindtouchstart="stopDevicePress">停止</view>
    </view>
  </view> -->
</view>