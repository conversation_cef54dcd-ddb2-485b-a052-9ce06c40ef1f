// pages/intervalplay/intervalplay.js
const app = getApp()
var user = require('../../utils/ble.js');
var devId;
var time = 0;
Page({

    /**
     * 页面的初始数据
     */
    data: {
        cancel: "取消",
        enter: "确认",
        text1: "重复",
        text2: "",
        text3: "选曲",
        text4: "",
        text5: "设置音量",
        text6: "继电器",
        text7: "播放间隔（分钟）",
        text8: "18:00",
        text9: "结束时间",
        text10: "19:00",
        text11: "周日",
        text12: "周一",
        text13: "周二",
        text14: "周三",
        text15: "周四",
        text16: "周五",
        text17: "周六",

        startTime: "/",
        endTime: "24:00",

        interval: "1",

        hiddenmodal: true,

        relayON: true,
        items: [
            { name: 0x01, value: '周日' ,checked:true},
          { name: 0x02, value: '周一', checked: true},
          { name: 0x04, value: '周二', checked: true},
          { name: 0x08, value: '周三', checked: true},
          { name: 0x10, value: '周四', checked: true},
          { name: 0x20, value: '周五', checked: true},
          { name: 0x40, value: '周六', checked: true},
        ],

        week: 0x00,
        weekvalue: [],
        i18n:[],
    },

    enter: function () {
        var num = parseInt(this.data.interval)
        
        console.log(num)

        if (this.data.week == 0) {
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["timeplay.tips.repeat"],
              image: '/images/error.png',
              duration: 1000
            })
          } else {
            wx.showModal({
              title: 'Tips',
              content: app.globalData.i18n["timeplay.tips.repeat"],
              showCancel: false,
              confirmText: "OK"
            })
          }
        } else if (app.globalData.newsong[0].songname == "") {
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["timeplay.tips.song"],
              image: '/images/error.png',
              duration: 1000
            })
          } else {
            wx.showModal({
              title: 'Tips',
              content: app.globalData.i18n["timeplay.tips.song"],
              showCancel: false,
              confirmText: "OK"
            })
          }
        } else if ((num < 1) || (num > 255)){           
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["intervalplay.tips.interval"],
              image: '/images/error.png',
              duration: 1000
            })
          } else {
            wx.showModal({
              title: 'Tips',
              content: app.globalData.i18n["intervalplay.tips.interval"],
              showCancel: false,
              confirmText: "OK"
            })
          }
        }else {
            app.globalData.newsong[0].relayON = this.data.relayON;
            app.globalData.newsong[0].week = this.data.week;
            app.globalData.newsong[0].startTime = this.data.startTime;
            app.globalData.newsong[0].endTime = this.data.endTime;
            app.globalData.newsong[0].weekstr = this.data.text2;
            app.globalData.newsong[0].loopstr = this.data.text4;
            app.globalData.newsong[0].interval = num;
            app.globalData.newsong[0].istimeon = false
            app.globalData.newsong[0].timeenble = true

            if (this.data.relayON) {
              app.globalData.newsong[0].relaystr = app.globalData.i18n["timer.relaystr.on"]
            } else {
              app.globalData.newsong[0].relaystr = app.globalData.i18n["timer.relaystr.off"]
            }
            console.log(app.globalData.newsong)

            app.globalData.isSongFlag = true;

          wx.showToast({
            title: app.globalData.i18n["timeplay.tips.settingok"],
            duration: 1000
          })


            wx.navigateBack({
                delta: 1
            })
        }
    },

    backlast: function () {
        wx.navigateBack({
            delta: 1
        })
    },

    getInput: function (e) {//方法1
        this.setData({
            interval: e.detail.value
        });
    },

    checkboxChange: function (e) {
        console.log('checkbox发生change事件，携带value值为：', e.detail.value)
        this.data.weekvalue = e.detail.value
        console.log(this.data.weekvalue)
    },

    switchonoff: function (e) {
        this.data.relayON = e.detail.value
    },

    procbind1: function () {
        this.setData({
            hiddenmodal: false
        })
    },

    procbind2: function () {
        wx.navigateTo({
            url: '../voicename/voicename',
        })
    },

    confirm: function () {
        var value = this.data.weekvalue
        var num = value.length
        if (num == 0) {
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["timeplay.tips.interval"],
              image: '/images/error.png',
              duration: 1000
            })
          }
          else {
            wx.showModal({
              title: app.globalData.i18n["Tips"],
              content: app.globalData.i18n["timeplay.tips.interval"],
              showCancel: false,
              confirmText: "OK"
            })
          }
        } else{
            this.data.week = 0;
            for (var i = 0; i < num; i++) {
                this.data.week += parseInt(value[i])
            }

            console.log(this.data.week)

            var str = ""
            if (this.data.week > 0) {
                if ((this.data.week & 0x01) == 0x01) {
                  str += app.globalData.i18n["timer.weekstr.0"]
                }
                if ((this.data.week & 0x02) == 0x02) {
                  str += app.globalData.i18n["timer.weekstr.1"]
                }
                if ((this.data.week & 0x04) == 0x04) {
                  str += app.globalData.i18n["timer.weekstr.2"]
                }
                if ((this.data.week & 0x08) == 0x08) {
                  str += app.globalData.i18n["timer.weekstr.3"]
                }
                if ((this.data.week & 0x10) == 0x10) {
                  str += app.globalData.i18n["timer.weekstr.4"]
                }
                if ((this.data.week & 0x20) == 0x20) {
                  str += app.globalData.i18n["timer.weekstr.5"]
                }
                if ((this.data.week & 0x40) == 0x40) {
                  str += app.globalData.i18n["timer.weekstr.6"]
                }
                console.log(str)
                str = str.substr(0, str.length - 1);
                console.log(str)

                this.setData({
                    text2: str
                })
            }

            this.setData({
                hiddenmodal: true
            })
        }
        
    },

    cancel: function () {
        this.setData({
            hiddenmodal: true
        })
    },
  containWeek: function (week, inWeeks) {
    //week:0~6表示星期日至六
    return (inWeeks >> week) & 0b0000001 == 1;
  }, 
  weekValueOfWeeks: function (weeks) {
    var wevalue = [];
    for (var i = 0; i < 7; i++) {
      // this.data.week += parseInt(value[i])
      if (this.containWeek(i, weeks)) {
        wevalue.push(1 << i);
      }
    }
    return wevalue;
  },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      this.setData({
        i18n: getApp().globalData.i18n,
        cancel: app.globalData.i18n["timeplay.cancel"],
        enter: app.globalData.i18n["timeplay.enter"],
        text1: app.globalData.i18n["timeplay.text1"],
        text2: app.globalData.i18n["timeplay.text2"],
        text3: app.globalData.i18n["timeplay.text3"],
        text4: app.globalData.i18n["timeplay.text4"],
        text5: app.globalData.i18n["timeplay.text5"],
        text6: app.globalData.i18n["timeplay.text6"],
        text7: app.globalData.i18n["intervalplay.interval"],
        text11: app.globalData.i18n["timeplay.week.0"],
        text12: app.globalData.i18n["timeplay.week.1"],
        text13: app.globalData.i18n["timeplay.week.2"],
        text14: app.globalData.i18n["timeplay.week.3"],
        text15: app.globalData.i18n["timeplay.week.4"],
        text16: app.globalData.i18n["timeplay.week.5"],
        text17: app.globalData.i18n["timeplay.week.6"],
        items: [
          { name: 0x01, value: app.globalData.i18n["timeplay.week.0"], checked: true},
          { name: 0x02, value: app.globalData.i18n["timeplay.week.1"], checked: true},
          { name: 0x04, value: app.globalData.i18n["timeplay.week.2"], checked: true},
          { name: 0x08, value: app.globalData.i18n["timeplay.week.3"], checked: true},
          { name: 0x10, value: app.globalData.i18n["timeplay.week.4"], checked: true},
          { name: 0x20, value: app.globalData.i18n["timeplay.week.5"], checked: true},
          { name: 0x40, value: app.globalData.i18n["timeplay.week.6"], checked: true},
        ],
        week: 127,
        weekvalue: this.weekValueOfWeeks(127),
      });
        if (app.globalData.ismodify) {
            // app.globalData.ismodify = false

            var weeks = app.globalData.newsong[0].week;
            this.setData({
                relayON: app.globalData.newsong[0].relayON,
                week: app.globalData.newsong[0].week,
                startTime: app.globalData.newsong[0].startTime,
                endTime: app.globalData.newsong[0].endTime,
                text2: app.globalData.newsong[0].weekstr,
                text4: app.globalData.newsong[0].loopstr,
                text3: app.globalData.newsong[0].songname,
                text5: app.globalData.i18n["timer.volume"] + app.globalData.newsong[0].volume,
                interval: app.globalData.newsong[0].interval,
              items: [
                { name: 0x01, value: app.globalData.i18n["timeplay.week.0"], checked: this.containWeek(0, weeks)  },
                { name: 0x02, value: app.globalData.i18n["timeplay.week.1"], checked: this.containWeek(1, weeks) },
                { name: 0x04, value: app.globalData.i18n["timeplay.week.2"], checked: this.containWeek(2, weeks) },
                { name: 0x08, value: app.globalData.i18n["timeplay.week.3"], checked: this.containWeek(3, weeks) },
                { name: 0x10, value: app.globalData.i18n["timeplay.week.4"], checked: this.containWeek(4, weeks) },
                { name: 0x20, value: app.globalData.i18n["timeplay.week.5"], checked: this.containWeek(5, weeks) },
                { name: 0x40, value: app.globalData.i18n["timeplay.week.6"], checked: this.containWeek(6, weeks) },
              ],
              week: weeks,
              weekvalue: this.weekValueOfWeeks(weeks),
            })

        }
        else {
            app.globalData.newsong = [{
                allLoop: false,
                isLoop: false,
                songnum: 1,
                songname: "",
                volume: 0,
                relayON: false,
                week: 0,
                startTime: "/",
                endTime: "",
                weekstr: "",
                loopstr: "",
                relaystr: "",
                id: 0,
                interval: 0,
                istimeon: true,
                timeenble: true,
                txtStyle: "",
            }]
        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        if (app.globalData.isSongFlag) {
            app.globalData.isSongFlag = false;
            var str = ""
            if (app.globalData.newsong[0].allLoop) {
                if (app.globalData.newsong[0].isLoop) {
                  str = app.globalData.i18n["timer.loostr.Allloop"]
                } else {
                  str = app.globalData.i18n["timer.loostr.Allnoloop"]
                }
            } else {
                if (app.globalData.newsong[0].isLoop) {
                  str = app.globalData.i18n["timer.loostr.Singleloop"]
                } else {
                  str = app.globalData.i18n["timer.loostr.Singlenoloop"]
                }
            }

            this.setData({
                text3: app.globalData.newsong[0].songname,
                text4: str,
              text5: app.globalData.i18n["timer.volume"] + app.globalData.newsong[0].volume,
            })
        }
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})