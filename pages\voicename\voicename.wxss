/* pages/voicename/voicename.wxss */
.pageset{
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    font-size: 18px;
    margin: 10px;
}

.pageset1{
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    font-size: 18px;
}

.pageset2{
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    font-size: 14px;
    margin: 15px;
}

.viewset1{
    margin-left: 30px;
    margin-right: 45px;
}

.viewset2{
    margin-left: 30px;
    margin-right: 45px;
    background-color: #808080;
}

.viewset3{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50%;
    height: 50px;
    background-color: #808080;
}

.viewset4{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50%;
    height: 50px;
    /* background-color: #808080; */
}

.viewset5{
    margin: 30px;
}

.divLine{
    background: #E0E3DA;
    height: 3rpx;
}

.btnImg {
  width: 30px;
  height: 30px;
  margin: 5px;
}

.page-timr4{
    width: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 5px;
}
.page-timr2{
    font-size: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px;
}
.page-timr3{
    display: flex;
    width: 100%;
    font-size: 15px;
    color: rgb(7, 5, 5);
    justify-content: center;
    align-items: center;
}

.page-timr6{
    display: flex;
    width: 100%;
    font-size: 15px;
    color: rgb(7, 5, 5);
    flex-direction:row-reverse;
    align-items: center;
}


.page-timr5{
    margin: 10px;
}

.page1 {
    height:50px;
  display: flex;
  color: rgb(3, 0, 0);
  justify-content: center;
  align-items: center;
  margin: 5px;
}

.page2 {
    height:50px;
  display: flex;
  color: white;
  justify-content: center;
  align-items: center;
  font-size: 30;
}

.btnImg1 {

  width: 46rpx;
  height: 40rpx;
}

.btnImg2 {
  width: 20px;
  height: 20px;
  margin: 5px;
}

.sliderset {
  width: 200px;
}

.th {
  width: 40%;
  justify-content: center;
  background: #3366FF;
  color: #fff;
  display: flex;
  height: 3rem;
  align-items: center;
  margin: 1px;
}
.td {
    width:40%;
    justify-content: center;
    text-align: center;
}