// pages/trigger/trigger.js
const app = getApp()
var user = require('../../utils/ble.js');
var devId;
var time = 0;
Page({

    /**
     * 页面的初始数据
     */
    data: {
        text1: "红外人体感应触发",
        text2: "磁性感应触发",
        text3: "微波感应触发",
        text4: "近距离感应触发",
        text5: "外部输入触发",
        text6: "上电响",
        text7: "继电器输出控制 1",
        text8: "设置同步",

        checked1: true,
        checked2: true,
        checked3: true,
        checked4: true,
        checked5: true,
        checked6: true,
        checked7: true,

        isChanged: false,
        isTrigger1: false,
        i18n:[],
        relaySettingEnable:1,//继电器功能
        playWhenPowerOnSettingEnable:1,//上电响
        proximitySensorEnable:1,// 近距离感应
        microwaveSensorEnable:1,// 微波感应
        magnetSensorEnable:1,// 磁性感应
        infraredSensorEnable:1,// 红外人体感应;
        outterSensorEnable:1,//外部感应        
    },

    change1: function (e) {
        this.setData({
            checked1: e.detail.value
        })

        this.setData({
            isChanged: true
        })
    },

    change2: function (e) {
        this.setData({
            checked2: e.detail.value
        })

        this.setData({
            isChanged: true
        })
    },

    change3: function (e) {
        this.setData({
            checked3: e.detail.value
        })

        this.setData({
            isChanged: true
        })
    },

    change4: function (e) {
        this.setData({
            checked4: e.detail.value
        })

        this.setData({
            isChanged: true
        })
    },

    change5: function (e) {
        this.setData({
            checked5: e.detail.value
        })

        this.setData({
            isChanged: true
        })
    },

    change6: function (e) {
        this.setData({
            checked6: e.detail.value
        })

        this.setData({
            isChanged: true
        })
    },

    change7: function (e) {
        this.setData({
            checked7: e.detail.value
        })

        this.setData({
            isChanged: true
        })
    },

    synchronize: function () {
        var that = this

        that.data.isTrigger1 = true;

        var buffer = new Uint8Array(4);
        buffer[0] = 0xAB;

        if (that.data.checked1) {
            buffer[1] += 0x01
        }
        if (that.data.checked2) {
            buffer[1] += 0x02
        }
        if (that.data.checked3) {
            buffer[1] += 0x04
        }
        if (that.data.checked4) {
            buffer[1] += 0x08
        }
        if (that.data.checked5) {
            buffer[1] += 0x10
        }
        if (that.data.checked6) {
            buffer[1] += 0x20
        }
        if (that.data.checked7) {
            buffer[1] += 0x40
        }

        var tem = buffer[0] + buffer[1]
        buffer[2] = tem >> 8
        buffer[3] = tem
        user.writeData(buffer)

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isTrigger1) {
                time++;
                if (time < 3) {

                    user.writeData(buffer)
                }
                else {
                    that.data.isTrigger1 = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["trigger.error"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })

    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this
        this.setData({
          i18n: app.globalData.i18n,
          text1: app.globalData.i18n["trigger.text1"],
          text2: app.globalData.i18n["trigger.text2"],
          text3: app.globalData.i18n["trigger.text3"],
          text4: app.globalData.i18n["trigger.text4"],
          text5: app.globalData.i18n["trigger.text5"],
          text6: app.globalData.i18n["trigger.text6"],
          text7: app.globalData.i18n["trigger.text7"],
          text8: app.globalData.i18n["trigger.text8"],
        });
        
        if(app.globalData.config != null)
        {
            this.setData({
                relaySettingEnable:app.globalData.config.detail.relaySettingEnable,//继电器功能
                playWhenPowerOnSettingEnable:app.globalData.config.detail.playWhenPowerOnSettingEnable,//上电响
                proximitySensorEnable:app.globalData.config.detail.proximitySensorEnable,// 近距离感应
                microwaveSensorEnable:app.globalData.config.detail.microwaveSensorEnable,// 微波感应
                magnetSensorEnable:app.globalData.config.detail.magnetSensorEnable,// 磁性感应
                infraredSensorEnable:app.globalData.config.detail.infraredSensorEnable,// 红外人体感应;
                outterSensorEnable:app.globalData.config.detail.outterSensorEnable,//外部感应
            })
        }
        var trigger = app.globalData.triggernum
        console.log(trigger)

        if ((trigger & 0x01) == 0) {
            that.setData({
                checked1: false,
            })
        }
        if ((trigger & 0x02) == 0) {
            that.setData({
                checked2: false,
            })
        }
        if ((trigger & 0x04) == 0) {
            that.setData({
                checked3: false,
            })
        }
        if ((trigger & 0x08) == 0) {
            that.setData({
                checked4: false,
            })
        }
        if ((trigger & 0x10) == 0) {
            that.setData({
                checked5: false,
            })
        }
        if ((trigger & 0x20) == 0) {
            that.setData({
                checked6: false,
            })
        }
        if ((trigger & 0x40) == 0) {
            that.setData({
                checked7: false,
            })
        }
        //=======================================================
        //recieve data
        user.triggerCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

            console.log('收到新数据t', bufView)

            if (that.data.isTrigger1) {
                console.log("isTrigger1")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                    that.data.isTrigger1 = false

                    wx.hideLoading()
                    wx.showToast({
                      title: app.globalData.i18n["trigger.synok"],
                        duration: 1000
                    })
                }
            }

        }    
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.triggerCallback = null;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})