// pages/language/language.js
var that;
const app = getApp();
var user = require('../../utils/ble.js');
var time;
Page({

  /**
   * 页面的初始数据
   */
  data: {
    lang:[{'value':'zh_CN','name':'普通话','checked':false},{'value':'en','name':'English','checked':false}],
    scrollHeight:'auto',
    currentIndex:-1,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    that = this;
    var scrollHeight = wx.getSystemInfoSync().windowHeight;
    this.setData({
      scrollHeight:scrollHeight,
      i18n: app.globalData.i18n,
    })
    console.log("当前语言:" + options.name);
    var langs = this.data.lang;
    this.data.currentIndex = langs.findIndex(function(obj,index){
      return obj.name == options.name;
    });
    if(this.data.currentIndex >= 0 ){
      langs[this.data.currentIndex].checked = true;
      this.setData({
        lang:langs,
      });
    }
    // this.queryLang();
    // this.setLangCheckedByValue(e.detail.value);
    user.languageSettingCallback = res => {
      var buf = new Uint8Array(res.value);
      var bufView = user.checkData(buf);

      console.log('收到新数据', user.hexstringFromBuffer(buf))
      if ((bufView[0] == 0x7E) && (bufView[3] == 0xC8) && (bufView[bufView.length - 1] == 0x7A)) {
        time = -999;
        console.log("播报语言设置成功"+bufView[4])
        wx.hideLoading();
        wx.showToast({
            title: that.data.i18n["lanugage.updateok"],
            duration: 1000
        })

        if(this.data.currentIndex >= 0 ){
          const eventChannel = this.getOpenerEventChannel()
          eventChannel.emit('acceptDataFromOpenedPage', {'language':this.data.lang[this.data.currentIndex].name});
        }
        //用户设置过了语言,不在连接时,自动设置语言;
        wx.setStorageSync("userSetLanguage"+ app.globalData.devId, bufView[4]);
      }
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    user.languageSettingCallback = null;
    time = -999;
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  },
  /**
   * 查询当前语言,当前语言收上个页面传输过来.
   */
  queryLang:function(){

  },
  /**根据语言设置为checked */
  setLangCheckedByValue(value){    
    var index = this.getLangeIndexByValue(value);

    if(index >= 0){
      var langs = this.data.lang;
      langs.forEach(element => {
        element.checked = false;
      });
      var currentLang = langs[index];
      currentLang.checked = true;
      langs[index] = currentLang;
      this.setData({
        lang:langs,
      });
    }
  },
  getLangeIndexByValue(value){
    var index = this.data.lang.findIndex(function(obj,index){
      return obj.value == value;
    },);
      
    return index;//没有则返回-1;
  },
  /**
   * 用户点击语言
   */
  langSettingChanged:function(e){
    // console.log(e.target.dataset.index);
    console.log('radio发生change事件，携带value值为：', e.detail.value)
    this.data.currentIndex = this.getLangeIndexByValue(e.detail.value);
    this.setLangCheckedByValue(e.detail.value);
  },
  /**
   * 发送语言到蓝牙
   */
  setLang:function(e){

    var index = this.data.currentIndex;
    if(index == -1){
      return;
    }

    var buffer = [0xA8, index, 0x00, 0xA8 + index];
    user.writeData(buffer);
    time = 0;
    var interval = setInterval(function () {
        if(time < 0){
          clearInterval(interval);
          wx.hideLoading();
          return;
        }
        time++;
        if (time < 3) {
            user.writeData(buffer);
        }
        else {
            clearInterval(interval);
            wx.hideLoading();
            wx.showToast({
              title: app.globalData.i18n["language.tips.settingerror"],
              image: '/images/error.png',
              duration: 1000
            })
        }
    }, 2000);

    wx.showLoading({
      title: app.globalData.i18n["control.tips.sending"],
        mask: true
    });

  },
})