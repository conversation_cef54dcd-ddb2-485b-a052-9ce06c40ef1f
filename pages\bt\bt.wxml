<!--pages/bt/bt.wxml-->
<wxs module="tool" src="../../utils/tool.wxs"></wxs>
<modal wx:if="{{!hiddenmodal}}" title="{{i18n['control.input.password']}}" confirm-text="{{i18n['control.input.connect']}}" cancel-text="{{i18n['cancel']}}" bindcancel="cancel" bindconfirm="confirm">
    <input type='text' class="viewset2" value="000000" maxlength="6" placeholder="Password" bindinput="getInput" auto-focus/> 
</modal>
<mp-loading type="circle" show="{{isPullingDown}}" tips="{{i18n['control.tips.loading']}}" ></mp-loading>
<view >
    <mp-cells title="{{unborded}}">
      <block wx:for="{{scanDevices}}" wx:key="deviceId">
        <mp-cell wx:if="{{scanTime - item.scanTime <= 5000}}" link hover style="width:100%;{{(scanTime - item.scanTime> 5000?'color:var(--weui-FG-1);':'')}}" bindtap="playsong" footer="{{(item.RSSI < 0?(item.RSSI+135):item.RSSI)}}" bindtap='bleClick' data-dev="{{item}}">
        <view>
        <!-- + " (" + (scanTime - item.scanTime> 7000?i18n['offline']:i18n['online']) + ")" -->
          <view>{{item.name}}</view>
          <view style="color: var(--weui-FG-1);font-size: 13px;overflow-x:hidden;white-space: wrap;" wx:if="{{item.lastConnect == 1}}">{{(debug=='develop'?item.deviceId:'') + " " + i18n['lastConnected']}}</view>
        </view>
        </mp-cell> 
      </block>
    </mp-cells>
    <!-- <view>{{unborded}}</view> -->
    <view hidden="{{!(scanTimeout == true && scanDevices.length == 0)}}" style="position: absolute;top: 45%;color: red;width: 100%;margin-left:20rpx;">
      <view>{{i18n['bt.notfoundTitle']}}</view>
      <view>{{i18n['bt.notfoundGPS']}}</view>
      <view>{{i18n['bt.notfoundBLE']}}</view>
      <view>{{i18n['bt.notfoundRelaunch']}}</view>
    </view>
    <!-- <view class='item-body' wx:for="{{scanDevices}}" wx:key="deviceId">    
      <view style="display:flex;">
        <view bindtap='bleClick' data-dev="{{item}}" class='item-text' style="float:left;width:100%;">
          {{item.name}} {{debug=="develop"?"":""}}        
        </view>
        <view class='item-text' style="float:right;margin-right:10px;">{{item.RSSI < 0?(item.RSSI+135):item.RSSI}}</view>
      </view>           
    </view> -->
</view>
