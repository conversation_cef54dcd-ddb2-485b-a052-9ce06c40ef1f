/* pages/bt/bt.wxss */
.page-timr1{
    width:100%;
    display: flex;
    
    font-size: 20px;
    margin-bottom: 10px;

    position: fixed;
    bottom: 10px;

    align-items: center;

    flex-direction: column;

}
.demo0{
  background-color: #f6f6f6;
}
.table {
  border: 0px solid darkgray;
}
.tr {
  display: flex;
  width: 100%;
  justify-content: center;
  height: 3rem;
  align-items: center;
}
.td {
    width:40%;
    justify-content: center;
    text-align: center;
}
.bg-w{
  background: snow;
}
.bg-g{
  background: #E6F3F9;
}
.th {
  width: 40%;
  justify-content: center;
  background: #3366FF;
  color: #fff;
  display: flex;
  height: 3rem;
  align-items: center;
}
.item-body {
  /* display: flex; */
  margin-left: 20px;
  margin-right: 20px; 
  /* justify-content: space-between;  */
  border-bottom: 1px solid rgb(190, 187, 187);
}

.item-text {
  padding-top: 10px;
  padding-bottom: 10px;
  margin-left: 10px;
  color: black;
}

.viewset2{
    text-align: center;
}

.modalset {
    margin-top: 60px;
}
/* pages/bt/bt.wxss */