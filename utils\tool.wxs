function hasSettingLanguage(uid){
  // var app = getApp();
  var userIndex = wx.getStorageSync("userSetLanguage" + uid);
  if(userIndex !== "" && userIndex !== null){
    return true;
  }
  return false;
}

function formatDuration (duration){
  if(duration == null || duration == undefined || duration < 0){
    return "";
  }
  var hour = parseInt(duration / 3600);
  var min = parseInt((duration - hour * 3600 - duration % 60)/60);
  var second = duration % 60;
  // var hourString = (hour > 0)?((hour > 9?hour:"0"+hour) + ":"):"";
  // var minString = (hour > 0)?(((min > 9)?min:"0"+min) + ":"):(min>0?(min>9?min:"0"+min)+":":"");
  var hourString = (hour > 0)?((hour > 9?hour:"0"+hour) + ":"):"00:";
  var minString = (hour > 0)?(((min > 9)?min:"0"+min) + ":"):(min>0?(min>9?min:"0"+min)+":":"00:");
  var secondString = second > 9?second:"0" + second;
  
  return (hourString + minString + secondString);
}
module.exports = {
  hasSettingLanguage: hasSettingLanguage,
  formatDuration:formatDuration
}