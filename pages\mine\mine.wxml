<!--pages/mine/mine.wxml-->
<!--pages/mine/mine.wxml-->
<view>
<view class="imagesize">
    <image class="avatar" src='{{userInfo.avatarUrl}}'></image>
    <view class="nickname">{{userInfo.nickName}}</view>
    <button open-type="getUserInfo" bindgetuserinfo="getUserInfo" hidden="{{ userInfo != '' && userInfo.nickName != ''}}">{{i18n["mine.userInfo"]}}</button>
</view>

<view class="divLine"></view>

<!--
<view class="textsize" bindtap="conset">
    <text>{{connectset}}</text>
    <view class="arrow"></view>
</view>

<view class="divLine"></view>
-->

<view class="textsize" bindtap="parameterset">
    <text>{{i18n["mine.settings"]}}</text>
    <view class="arrow"></view>
</view>

<view class="divLine"></view>

<view class="textsize" bindtap="aboutus">
    <text>{{i18n["mine.aboutus"]}}</text>
    <view class="arrow"></view>
</view>

<view class="divLine"></view>
<view class="textsize" bindtap="disconnect">
    <text>{{i18n["mine.disconnect"]}}</text>
</view>
<view class="divLine"></view>
</view>

