/* pages/set/set.wxss */
.textsize { 
    display:flex;
    align-items: center;
    margin: 10px;

  } 

.divLine{
    background: #E0E3DA;
    height: 3rpx;
}

.textsize1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px;
}

.arrow{
 
width: 10px;
 
height: 10px;
 
border-top: 2px solid #999;
 
border-right: 2px solid #999;
 
position: absolute;
 
right: 20rpx;
 
transform: rotate(45deg);
 
margin-top:3rpx;
 
}
.value{
  position: absolute;
  right: 40px;
}