/* pages/timeplay/timeplay.wxss */

/* pages/devicerelated/devicerelated.wxss */
.btnImg {
  width: 96%;
  height: 50px;
  margin: 5px;
}

.inputset{
    color: #aaa;
    width: 80px;
    height: 30px;
    text-align: center;
    border: solid 1px;
    font-size: 20px;
}

.pageset{
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    font-size: 18px;
    margin: 10px;
}

.pageset2{
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    font-size: 14px;
    margin: 15px;
}

.viewset1{
    margin-left: 30px;
    margin-right: 45px
}

.viewset2{
    margin-left: 5px;
}

.viewset3{
    margin-right: 35px
}

.viewset4{
    font-size: 18px;
    margin-right: 40px
}

.divLine{
    background: #E0E3DA;
    height: 3rpx;
}

.arrow{
    width: 10px;
    height: 10px;
    border-top: 2px solid #999;
    border-right: 2px solid #999;
    position: absolute;
    right: 20rpx;
    transform: rotate(45deg);
    /* margin-top:10rpx; */
}