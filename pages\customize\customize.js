// pages/customize/customize.js
const app = getApp()
var user = require('../../utils/ble.js');
var util = require('../../utils/util.js');
var time = 0;
Page({

    /**
     * 页面的初始数据
     */
    data: {
        datetext: "日期",
        timetext: "继电器时间",
        key1: "Key1",
        key2: "Key2",
        sendtext: "发送",

        inputtext1: "",
        inputtext2: "",
        inputtext3: "",
        inputtext4: "",

        isCustomize:false,
        isSettime:false,
        isRelay:false,
        isKey1: false,
        isKey2: false,
        relaySettingEnable:1,
        location:{authorize:false,longitude:0,latitude:0,reject:false}        
    },

    getInput1: function (e) {//方法1
        this.setData({
            inputtext1: e.detail.value
        });
    },

    getInput2: function (e) {//方法1
        this.setData({
            inputtext2: e.detail.value
        });
    },

    getInput3: function (e) {//方法1
        this.setData({
            inputtext3: e.detail.value
        });
    },

    getInput4: function (e) {//方法1
        this.setData({
            inputtext4: e.detail.value
        });
    },

    send1: function(){
        var that = this

        that.data.isSettime = true;

        var date = new Date()

        var year = date.getFullYear()
        var month = date.getMonth() + 1
        var day = date.getDate()
        var hour = date.getHours()
        var minute = date.getMinutes()
        var second = date.getSeconds()
        var week = date.getDay();

        var num = 11;
        var buf0 = new Uint8Array(num);
        buf0[0] = 0xA5
        buf0[1] = year >> 8;
        buf0[2] = year
        buf0[3] = month
        buf0[4] = week
        buf0[5] = day
        buf0[6] = hour
        buf0[7] = minute
        buf0[8] = second

        var tem = buf0[0];
        for (var i = 1; i < (num - 2); i++) {
            tem += buf0[i];
        }

        buf0[num - 2] = tem >> 8
        buf0[num - 1] = tem

        user.writeData(buf0)

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isSettime) {
                time++;
                if (time < 3) {

                    user.writeData(buf0)
                }
                else {
                    that.data.isSettime = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["control.tips.adjustdateerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.adjustingdate"],
            mask: true
        })
    },

    send2: function () {
        var that = this
        that.data.isRelay = true;

        var str = this.data.inputtext2
        var num = parseInt(str)

        if ((num >= 3)&&(num <= 99)){
            var buffer = new Uint8Array(4);
            buffer[0] = 0xCB;
            buffer[1] = num;
            var tem = buffer[0] + buffer[1]
            buffer[2] = tem >> 8
            buffer[3] = tem
            user.writeData(buffer)

            time = 0;
            var interval = setInterval(function () {
                if (that.data.isRelay) {
                    time++;
                    if (time < 3) {
                        user.writeData(buffer)
                    }
                    else {
                        that.data.isRelay = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        if (wx.getStorageSync("language") == "zh_CN") {
                          wx.showToast({
                            title: app.globalData.i18n["control.tips.sendingFailed"],
                            image: '/images/error.png',
                            duration: 1000
                          })
                        } else {
                          wx.showModal({
                            title: 'Tips',
                            content: app.globalData.i18n["control.tips.sendingFailed"],
                            showCancel: false,
                            confirmText: "OK"
                          })
                        }
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }
        else{
            
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["customize.number"],
              image: '/images/error.png',
              duration: 1000
            })
          } else {
            wx.showModal({
              title: 'Tips',
              content: app.globalData.i18n["customize.number"],
              showCancel: false,
              confirmText: "OK"
            })
          }

        }

        
    },

    send3: function () {
        var that = this
        if (this.data.inputtext3.length == 4) {
            var str = this.data.inputtext3

            var buf0 = new Uint8Array(str.length + 3);
            buf0[0] = 0xCC
            var tem = buf0[0];
            for (var i = 0; i < str.length; i++) {
                buf0[i + 1] = str.charCodeAt(i);
                tem += buf0[i + 1];
            }

            buf0[str.length + 1] = tem >> 8
            buf0[str.length + 2] = tem

            user.writeData(buf0)

            that.data.isKey1 = true;

            time = 0;
            var interval = setInterval(function () {
                if (that.data.isKey1) {
                    time++;
                    if (time < 3) {

                        user.writeData(buf0)
                    }
                    else {
                        that.data.isKey1 = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        if (wx.getStorageSync("language") == "zh_CN") {
                          wx.showToast({
                            title: app.globalData.i18n["control.tips.sendingFailed"],
                            image: '/images/error.png',
                            duration: 1000
                          })
                        } else {
                          wx.showModal({
                            title: 'Tips',
                            content: app.globalData.i18n["control.tips.sendingFailed"],
                            showCancel: false,
                            confirmText: "OK"
                          })
                        }
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }
        else {
            
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["customize.key4"],
              image: '/images/error.png',
              duration: 1000
            })
          } else {
            wx.showModal({
              title: 'Tips',
              content: app.globalData.i18n["customize.key4"],
              showCancel: false,
              confirmText: "OK"
            })
          }
        }
    },

    send4: function () {
        var that = this
        if (this.data.inputtext4.length == 4) {
            var str = this.data.inputtext4

            var buf0 = new Uint8Array(str.length + 3);
            buf0[0] = 0xCD
            var tem = buf0[0];
            for (var i = 0; i < str.length; i++) {
                buf0[i + 1] = str.charCodeAt(i);
                tem += buf0[i + 1];
            }

            buf0[str.length + 1] = tem >> 8
            buf0[str.length + 2] = tem

            user.writeData(buf0)

            that.data.isKey2 = true;

            time = 0;
            var interval = setInterval(function () {
                if (that.data.isKey2) {
                    time++;
                    if (time < 3) {

                        user.writeData(buf0)
                    }
                    else {
                        that.data.isKey2 = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        if (wx.getStorageSync("language") == "zh_CN") {
                          wx.showToast({
                            title: app.globalData.i18n["control.tips.sendingFailed"],
                            image: '/images/error.png',
                            duration: 1000
                          })
                        } else {
                          wx.showModal({
                            title: 'Tips',
                            content: app.globalData.i18n["control.tips.sendingFailed"],
                            showCancel: false,
                            confirmText: "OK"
                          })
                        }
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }
        else {
            
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["customize.key4"],
              duration: 1000
            })
          } else {
            wx.showModal({
              title: 'Tips',
              content: app.globalData.i18n["customize.key4"],
              showCancel: false,
              confirmText: "OK"
            })
          }
        }
    },

    // send4: function () {

    // },

    sendCA: function () {
        var that = this

        that.data.isCustomize = true;

        var buffer = [0xCA, 0x00, 0xCA];
        user.writeData(buffer)

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isCustomize) {
                time++;
                if (time < 3) {

                    user.writeData(buffer)
                }
                else {
                    that.data.isCustomize = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["control.tips.sending"],
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.loading"],
            mask: true
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this
        this.setData({
          i18n:app.globalData.i18n,
          datetext: app.globalData.i18n["customize.datetext"],
          timetext: app.globalData.i18n["customize.timetext"],
          sendtext: app.globalData.i18n["customize.sendtext"],
        })
        if(app.globalData.config != null)
        {
            this.setData({
                relaySettingEnable:app.globalData.config.detail.relaySettingEnable,//继电器功能
            })
        }

        var interval = setInterval(function () {

            that.setData({
                inputtext1: util.formatTime(new Date())
            })
        }, 500)

        that.sendCA();

        app.globalData.songName = [];
        that.setData({
            sData: app.globalData.songName
        })
        
        //=======================================================
        //recieve data
        user.customizeCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据c', user.hexstringFromBuffer(buf))

            if (that.data.isCustomize) {
                console.log("isCustomize")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x0A) && (bufView[3] == 0xB3) && (bufView[15] == 0x7A)) {
                    that.data.isCustomize = false

                    wx.hideLoading();

                    var buf = new Uint8Array(4);
                    buf[0] = bufView[5];
                    buf[1] = bufView[6];
                    buf[2] = bufView[7];
                    buf[3] = bufView[8];

                    var str1 = String.fromCharCode.apply(null, new Uint8Array(buf));

                    buf[0] = bufView[9];
                    buf[1] = bufView[10];
                    buf[2] = bufView[11];
                    buf[3] = bufView[12];

                    var str2 = String.fromCharCode.apply(null, new Uint8Array(buf));

                    that.setData({
                        inputtext2: bufView[4],
                        inputtext3: str1,
                        inputtext4: str2,
                    })
                }
            }

            if(that.data.isSettime){
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                    that.data.isSettime = false
                    
                    wx.hideLoading()

                    wx.showToast({
                      title: app.globalData.i18n["SendOK"],
                        duration: 1000
                    })
                }
            }

            if (that.data.isRelay) {
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                    that.data.isRelay = false

                    wx.hideLoading()

                    wx.showToast({
                      title: app.globalData.i18n["SendOK"],
                        duration: 1000
                    })
                }
            }

            if (that.data.isKey1) {
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                    that.data.isKey1 = false

                    wx.hideLoading()

                    wx.showToast({
                      title: app.globalData.i18n["SendOK"],
                        duration: 1000
                    })
                }
            }

            if (that.data.isKey2) {
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                    that.data.isKey2 = false

                    wx.hideLoading()

                    wx.showToast({
                      title: app.globalData.i18n["SendOK"],
                        duration: 1000
                    })
                }
            }


        }    
    },
    chooseLocation:function(e){
      var that = this;
      // wx.getSetting({
      //   success: (res) => {
      //     if(res.authSetting["scope.userLocation"] || res.authSetting["scope.userLocationBackground"]){
      //       wx.getLocation({
      //         altitude: 'false',
      //         success:(res)=>{
      //           if(that.data.location.authorize == false){
      //             that.setData({
      //               location:{authorize:true, latitude:res.latitude.toFixed(5), longitude:res.longitude.toFixed(5)},
      //             })
      //           }
      //         }
      //       })
      //     }
      //   },
      //   fail:(res)=>{
          
      //   }
      // })

      wx.chooseLocation({
        success: (res) => {
          console.log("纬度"+res.latitude+",经度"+res.longitude)
          that.setData({
            location:{authorize:true, latitude:res.latitude.toFixed(5), longitude:res.longitude.toFixed(5),reject:false},
          })
        },fail:(res) => {
          console.log(res);
          // if(res.errMsg == "chooseLocation:fail auth deny"){

          // }
          if(res.errMsg != "chooseLocation:fail cancel" && res.errMsg != "chooseLocation:fail"){
            wx.showToast({
              title: app.globalData.i18n['customize.getlocationFail']
            })
            that.setData({
              location:{authorize:false, latitude:0, longitude:0,reject:true},
            })
          }
        },
      })
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
      var that = this;
      wx.getSetting({
        success: (res) => {
          if(res.authSetting["scope.userLocation"] || res.authSetting["scope.userLocationBackground"]){
            wx.getLocation({
              altitude: 'false',
              success:(res)=>{
                if(that.data.location.authorize == false){
                  that.setData({
                    location:{authorize:true, latitude:res.latitude.toFixed(5), longitude:res.longitude.toFixed(5),reject:that.data.location.reject},
                  })
                }
              },fail:(res)=>{
                console.log(res);
              }
            })
          }
        },
      })
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.customizeCallback = null;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})