// pages/voicename/voicename.js
const app = getApp()
var user = require('../../utils/ble.js');
var time = 0;
Page({

    /**
     * 页面的初始数据
     */
    data: {
        cancel: "取消",
        enter: "确认",
        text1: "自选曲目",
        text2: "全部循环",
        sData: [],
        playurl: "/images/playmini.png",

        class1: "viewset3",
        class2: "viewset4",

        choiced: false,
        isLoop:false,
        songnum:0,
        isVoice: false,
        checkData:[],

        volume: 16,
        volumevalue: 16,

        isPlay:false,
        isSetvolume:false,
        interval: "",      //定时器
        i18n:[],
    },

    init: function (that) {
        var time = 60;
        var interval = ""
        that.clearTimeInterval(that)
        that.setData({
            time: time,
            interval: interval,
        })
    },


    clearTimeInterval: function (that) {
        var interval = that.data.interval;
        clearInterval(interval)
    },

    restartTap: function () {
        var that = this;
        that.init(that);
        console.log("倒计时重新开始")
        that.startTap()
    },

    startTap: function () {
        var that = this;
        that.init(that);          //这步很重要，没有这步，重复点击会出现多个定时器

        var interval = setInterval(function () {
            if (that.data.isResend) {
                time++;
                if (time < 3) {
                    var buffer = [0xAA, 0x00, 0xAA];
                    user.writeData(buffer)
                }
                else {
                    that.data.isResend = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.voicerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }
        }, 5000)

        that.setData({
            interval: interval
        })
    },

    playsong: function (e) {
        var that = this
        that.data.isPlay = true;

        var index = parseInt(e.currentTarget.dataset.index)

        var str = this.data.sData[index]
        var name = new Uint8Array(8);
        for (var i = 0; i < 8; i++) {
            name[i] = str.charCodeAt(i);
        }

        var buffer = new Uint8Array(13);
        buffer[0] = 0xC1;
        buffer[1] = 0x01;
        buffer[2] = index + 1;
        buffer[3] = name[0];
        buffer[4] = name[1];
        buffer[5] = name[2];
        buffer[6] = name[3];
        buffer[70] = name[4];
        buffer[8] = name[5];
        buffer[9] = name[6];
        buffer[10] = name[7];
        var num = 0;
        for (var i = 0; i < 11; i++) {
            num += buffer[i];
        }
        buffer[11] = num >> 8;
        buffer[12] = num;
        user.writeData(buffer)

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isPlay) {
                time++;
                if (time < 3) {
                    user.writeData(buffer)
                }
                else {
                    that.data.isPlay = false
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.playerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })
    },

    allLoop: function (e) {
        console.log('radio发生change事件，携带的value值为：', e.detail.value)
        this.data.isLoop = e.detail.value
    },

    singleLoop: function (e) {
        console.log('radio发生change事件，携带的value值为：', e.detail.value)
        this.data.isLoop = e.detail.value
    },

    volchange: function (e) {
        this.data.volume = e.detail.value

        var that = this
        var buffer = new Uint8Array(4);

        buffer[0] = 0xC4;
        buffer[1] = that.data.volume;
        buffer[2] = 0x00;
        buffer[3] = buffer[0] + buffer[1];
        user.writeData(buffer)

        that.data.isSetvolume = true

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isSetvolume) {
                time++;
                if (time < 3) {

                    user.writeData(buffer)
                }
                else {
                    that.data.isSetvolume = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    if (wx.getStorageSync("language") == "zh_CN") {
                      wx.showToast({
                        title: app.globalData.i18n["control.tips.sendingFailed"],
                        image: '/images/error.png',
                        duration: 1000
                      })
                    } else {
                      wx.showModal({
                        title: 'Tips',
                        content: app.globalData.i18n["control.tips.sendingFailed"],
                        showCancel: false,
                        confirmText: "OK"
                      })
                    }
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })
    },

    checkboxChange: function(e){
        console.log('radio发生change事件，携带的value值为：', e.detail.value)
        var str = e.detail.value
        if(str.length > 0)
        {
            if (this.data.checkData.length > 0){
                var temp = 0;
                for (var i = 0; i < this.data.checkData.length;i++){
                    if (str == this.data.checkData[i]){
                        temp = 1;
                    }
                }
                if (temp == 0){
                    this.data.checkData.push(str);
                }
            }else{
                this.data.checkData.push(str);
            }
        }
        console.log(this.data.checkData)
    },

    radiochange: function (e) {
        console.log('radio发生change事件，携带的value值为：', e.detail.value)
        // this.data.songnum = e.detail.value;
        this.data.songnum = parseInt(e.detail.value);
        // console.log(this.data.songnum)
    },


    procbind1: function () {
        if (this.data.choiced) {
            this.setData({
                choiced: false,
                class1: "viewset3",
                class2: "viewset4",
                isLoop: false,
            })
        }
    },

    procbind2: function () {
        if (!this.data.choiced) {
            this.setData({
                choiced: true,
                class1: "viewset4",
                class2: "viewset3",
                isLoop:false,
            })

        }
    },

    enter:function(){
        if (this.data.choiced){
            this.data.songnum = 0;
        }

        app.globalData.newsong = [{
            allLoop: false,
            isLoop: false,
            songnum: 1,
            songname: "",
            volume: 0,
            relayON: false,
            week: 0,
            startTime: "",
            endTime: "",
            weekstr:"",
            loopstr: "",
            relaystr: "",
            id: 0,
            interval: 0,
            istimeon: true,
        }]
        
        app.globalData.newsong[0].allLoop = this.data.choiced;
        app.globalData.newsong[0].isLoop = this.data.isLoop;
        app.globalData.newsong[0].songnum = this.data.songnum + 1;
        app.globalData.newsong[0].songname = this.data.sData[this.data.songnum];
        app.globalData.newsong[0].volume = this.data.volume;
        console.log(app.globalData.newsong)

        app.globalData.isSongFlag = true;

        wx.showToast({
          title: app.globalData.i18n["timeplay.tips.settingok"],
            duration: 1000
        })

        wx.navigateBack({
            delta: 1
        })
    },

    setdefault: function () {
        if (this.data.choiced) {
            this.data.songnum = 0;
        }

        app.globalData.newsong = [{
            allLoop: false,
            isLoop: false,
            songnum: 1,
            songname: "",
            volume: 0,
            relayON: false,
            week: 0,
            startTime: "",
            endTime: "",
            weekstr: "",
            loopstr: "",
            relaystr: "",
            id: 0,
            interval: 0,
            istimeon: true,
            txtStyle: "",
        }]

        app.globalData.newsong[0].allLoop = this.data.choiced;
        app.globalData.newsong[0].isLoop = this.data.isLoop;
        app.globalData.newsong[0].songnum = 1;
        app.globalData.newsong[0].songname = this.data.sData[0];
        app.globalData.newsong[0].volume = this.data.volume;
        console.log(app.globalData.newsong)

        app.globalData.isSongFlag = true;

        wx.showToast({
          title: app.globalData.i18n["timeplay.tips.settingok"],
            duration: 1000
        })

        wx.navigateBack({
            delta: 1
        })
    },

    sendAA: function () {
        
        var that = this

        if (!that.data.isVoice) {
            console.log(!that.data.isVoice)
            console.log(that.data.isVoice)

            app.globalData.songName = [];
            that.setData({
                sData: app.globalData.songName
            })

            that.data.isVoice = true;
            that.data.isResend = true

            var buffer = [0xAA, 0x00, 0xAA];
            user.writeData(buffer)

            time = 0;
            that.startTap()
            
            wx.showLoading({
              title: app.globalData.i18n["control.tips.loading"],
                mask: true
            })
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this
        this.setData({
          choiced: app.globalData.newsong[0].allLoop,
          isLoop: app.globalData.newsong[0].isLoop,
          cancel: app.globalData.i18n["cancel"],
          enter: app.globalData.i18n["confirm"],
          text1: app.globalData.i18n["voicename.Optionaltracks"],
          text2: app.globalData.i18n["timer.loostr.Allloop"],
          i18n:app.globalData.i18n,
          class1: app.globalData.newsong[0].allLoop ? "viewset4" :"viewset3",
          class2: app.globalData.newsong[0].allLoop ? "viewset3" : "viewset4",          
        });
        that.sendAA();//为什么还在此查询,多余的.

        app.globalData.songName = [];//为什么要清空旧数据?,此数据结构与设备语音管理不同,可能会出问题吧.
        that.setData({
            sData: app.globalData.songName
        })

        if (app.globalData.ismodify) {
            that.setData({
                volume: app.globalData.newsong[0].volume
            })
        }

        // app.globalData.newsong = []//为什么要清空旧数据?

        console.log(that.data.volume)
        //=======================================================
        //recieve data
        user.voiceCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据v', user.hexstringFromBuffer(buf))

            // if (that.data.isVoice) {
            //     console.log("isVoice")
            if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x09) && (bufView[3] == 0xB2) && (bufView[14] == 0x7A)) {
              //歌曲列表,返回多次.
                that.data.isResend = false
                that.clearTimeInterval(that)
                wx.hideLoading();

                var buf = new Uint8Array(8);
                buf[0] = bufView[4];
                buf[1] = bufView[5];
                buf[2] = bufView[6];
                buf[3] = bufView[7];
                buf[4] = bufView[8];
                buf[5] = bufView[9];
                buf[6] = bufView[10];
                buf[7] = bufView[11];

                var str = String.fromCharCode.apply(null, new Uint8Array(buf));

                app.globalData.songName.push(str);

                that.setData({
                    sData: app.globalData.songName,
                    songnum: app.globalData.newsong[0].songnum - 1,
                })

                // that.data.songnum = that.data.sData.length - 1;
                console.log(that.data.songnum)
            }

            if((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xBC) && (bufView[7] == 0x7A)) {
                that.data.isVoice = false
                that.data.isResend = false
                that.clearTimeInterval(that)
                wx.hideLoading();
            }

            if (that.data.isPlay) {
                console.log("isPlay")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[4] == 0xC1) && (bufView[7] == 0x7A)) {
                    that.data.isPlay = false

                    wx.hideLoading();
                }
            }

            if (that.data.isSetvolume) {
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)){
                    that.data.isSetvolume = false
                    wx.hideLoading()
                }
                

            }

             
        
        }   
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.voiceCallback = null;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
        var that = this

        that.sendAA();


        wx.stopPullDownRefresh();
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})