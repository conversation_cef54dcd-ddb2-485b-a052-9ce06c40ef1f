<view class="selectindex-container">
  <view class="select-types">
    <view
      wx:for="{{dataList}}"
      wx:key="index"
      class="select-types-item {{currentType === item.value ? 'active' : ''}}"
      bindtap="handleTypeClick"
      data-type="{{item.value}}"
      data-item="{{item}}"
    >
      <image class="select-types-item-icon" src="{{currentType === item.value ? item.iconActive : item.icon}}" mode="widthFix"></image>
      <text class="select-types-item-text">{{item.name}}</text>
    </view>
  </view>
  <view class="select-types-content">
    <view class="select-types-content-title">共{{currentItem.children.length}}款 控制程序</view>
    <view wx:for="{{currentItem.children}}" wx:key="index" class="select-types-content-item {{item.disabled ? 'disabled' : ''}}" bindtap="handleItemClick" data-item="{{item}}">
      <text class="select-types-content-item-text">{{item.name}}</text>
      <image class="select-types-content-item-icon" src="{{item.disabled ? '../../images/icons/right.png' : '../../images/icons/rightActive.png'}}" mode="widthFix"></image>
    </view>
  </view>
</view>

<view class="disabled-dialog" wx:if="{{disabledDialogShow}}">
  <view class="disabled-dialog-content">
    <image class="disabled-dialog-content-icon" src="../../images/icons/warning.png" mode="widthFix"></image>
    <view class="disabled-dialog-content-title">
      功能正在开发中，敬请期待！
    </view>
    <view class="disabled-dialog-content-btn" bindtap="handleDisabledDialogClose">
      我知道了
    </view>
  </view>
</view>