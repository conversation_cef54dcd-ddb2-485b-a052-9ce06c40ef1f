// pages/set/set.js
const app = getApp();
var user = require('../../utils/ble.js');
var time = 0;
// var util = require('../../utils/util.js');
// const langData = require('../../langData.js');
// //读取默认语言
// const lg = wx.getStorageSync('language');
Page({

    /**
     * 页面的初始数据
     */
  data: {
        blsname: "蓝牙名称",
        password: "密码修改",
        batterry: "电量",
        batvalue: "80",
        i18n: [],
        language:"",
    },

    blename: function () {
        var that = this;
        wx.navigateTo({
            url: '../blename/blename',
            events: {
                // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
                acceptDataFromOpenedPage: function(data) {
                       if(data.blsname != undefined){
                        that.setData({
                            blsname:data.blsname,
                        })
                    }
                },
              }
        })
    },

    password: function () {
        wx.navigateTo({
            url: '../password/password'
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this
        //util.resetSetData.call(this, langData);
        this.setData({
          i18n: app.globalData.i18n,
          blsname:app.globalData.devName,
        });
        that.data.isBatterry = true;

        var buffer = [0xD5, 0x00, 0xD5];
        user.writeData(buffer)

        time = 0;
        var interval = setInterval(function () {
            if(time < 0){
                clearInterval(interval);
                wx.hideLoading();
                return;
            }
            if (that.data.isBatterry) {
                time++;
                if (time < 3) {

                    user.writeData(buffer)
                }
                else {
                    that.data.isBatterry = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["set.tips.batter"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 2000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.loading"],
            mask: true
        })

        user.settingListCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据', user.hexstringFromBuffer(buf))

            if (that.data.isBatterry) {
                console.log("isBatterry")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xBB) && (bufView[7] == 0x7A)) {
                    that.data.isBatterry = false

                    that.setData({
                        batvalue: bufView[4]
                    })
                    //是否查询语言,旧版本没有配置项,也没有语言切换功能.
                    // var config = app.globalData.config;
                    // config = config.filter(function(obj,index){
                    //    return obj.devId == app.globalData.devId;
                    // });
                    // if (config.length < 0) 
                    if(app.globalData.config == null)
                    {
                        wx.hideLoading();
                    }else{
                        // config = config[config.length - 1];
                        that.queryLanguage();
                        // if(config.detail.broadcastEnglishEnable == 1)
                        if(app.globalData.config.detail.broadcastEnglishEnable == 1)
                        {
                            
                        }else{
                            wx.hideLoading();
                        }
                    }
                }
            }
            
            if ((bufView[0] == 0x7E) && (bufView[3] == 0xC9) && (bufView[bufView.length - 1] == 0x7A)) {
                console.log("收到播报语言"+bufView[4])
                wx.hideLoading();
                time = -999;
                switch(bufView[4]){
                    case 0:
                        that.setData({
                            language:"普通话",
                        });
                        break;
                    case 1:
                        that.setData({
                            language:"English",
                        })
                        break;
                }
            }            
        }    
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.settingListCallback = null;
      time = - 999;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    languagesetting:function(){
        var that = this;
        wx.navigateTo({
          url: '../language/language?name=' + this.data.language,
          events: {
            // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
            acceptDataFromOpenedPage: function(data) {
                if(data.language != undefined)
                {
                    that.setData({
                        language:data.language,
                    })
                }              
            },
          }
        })
    },
    queryLanguage:function(){
        var that = this;
        console.log("查询播报的语言");
        var buffer = [0xD9, 0x00, 0xD9];
        user.writeData(buffer);
        time = 0;
        var interval = setInterval(function () {
            if(time < 0){
                clearInterval(interval);
                wx.hideLoading();
                return;
            }
            time++;
            if (time < 3) {
                user.writeData(buffer);
            }
            else {
                clearInterval(interval);
                wx.hideLoading();
                // wx.showToast({
                //   title: app.globalData.i18n["set.tips.batter"],
                //   image: '/images/error.png',
                //     duration: 1000
                // })
            }
        }, 2000)
    },
})