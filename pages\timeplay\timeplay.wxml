<!--pages/timeplay/timeplay.wxml-->
<!--pages/devicerelated/devicerelated.wxml-->
<modal hidden="{{hiddenmodal}}" confirm-text="{{i18n['confirm']}}" cancel-text="{{i18n['cancel']}}" bindcancel="cancel" bindconfirm="confirm">
    <checkbox-group bindchange="checkboxChange">
  <label class="checkbox" wx:for="{{items}}">
    <view class="divLine"></view>
    <view class="pageset">
        <view>{{item.value}}</view>
        <checkbox value="{{item.name}}" checked="{{item.checked}}"/>
    </view>
  </label>
</checkbox-group>
</modal>

<view>
    <view class="pageset" >
        <view class="viewset1" bindtap="backlast">{{cancel}}</view>
        <!-- <view class="viewset1" bindtap="enter">{{cancel}}</view> -->
        <view class="viewset1"  bindtap="enter">{{enter}}</view>
    </view>

    <view class="divLine"></view>

    <view class="pageset2" bindtap="procbind1">
        <view class="viewset2">{{text1}}</view>
        <view class="viewset3">{{text2}}</view>
        <view class="arrow"></view>
    </view>

    <view class="divLine"></view>

    <view class="pageset2" bindtap="procbind2">
        <view class="viewset2">{{text3}}</view>
        <view class="viewset3">{{text4}}</view>
        <view class="viewset3">{{text5}}</view>
        <view class="arrow"></view>
    </view>

    <view class="divLine"></view>

     <view class="pageset2">
        <view class="viewset2">{{text6}}</view>
        <switch checked="{{relayON}}" class="viewset3" bindchange="switchonoff"></switch>
    </view>

    <view class="divLine"></view>

    <view class="pageset2">
        <view class="viewset2">{{text7}}</view>
        <view class="viewset4">
            <picker mode="time" value="{{startTime}}" bindchange="bindstartTimeChange">
                <view class="picker">
                    {{startTime}}
                </view>
            </picker>
        </view>
        
    </view>

    <view class="divLine"></view>

    <view class="pageset2">
        <view class="viewset2">{{text9}}</view>
        <view class="viewset4">
            <picker mode="time" value="{{endTime}}" bindchange="bindendTimeChange">
                <view class="picker">
                    {{endTime}}
                </view>
            </picker>
        </view>
    </view>

    <view class="divLine"></view>

</view>

