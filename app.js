const langData = require('./langData.js');

//app.js
App({
  onLaunch: function () {

    wx.getSystemInfo({
      success: function (res) {
        // console.log(res);
          var lang = res.language;          
          // var lang = wx.getSystemInfoSync().language;
          if (lang != 'zh_CN') {
            lang = 'en';
          }
          //界面的语言切换了,则播报语言记录清空,这样就会自动设置播放语言;
          if(lang != wx.getStorageSync('language')){
            wx.setStorageSync('userSetLanguage', "");
          }
          //根据后台返回数据存储，这里写死
          wx.setStorageSync("language", lang);
          wx.setNavigationBarTitle({
            title: lang != 'zh_CN' ? "Voice Reminder" : "语音提示器",
          });
      }
    });
    
    // 展示本地存储能力
    // var logs = wx.getStorageSync('logs') || []
    // logs.unshift(Date.now())
    // wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
    // 获取用户信息
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取头像昵称，不会弹框
          wx.getUserInfo({
            success: res => {
              // 可以将 res 发送给后台解码出 unionId
              this.globalData.userInfo = res.userInfo

              // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
              // 所以此处加入 callback 以防止这种情况
              if (this.userInfoReadyCallback) {
                this.userInfoReadyCallback(res)
              }
            }
          })
        }
      }
    })
  },
  globalData: {
    userInfo: null,
    password: null,
    triggernum: 0, 
    playnum:0,
    songName: [],
    timeData: [],
    newsong: [],
    isSongFlag:false,
    id:0,
    ismodify:false,
    secondBuf:[],
    uuidpassword:[],
    isSendOK:false,
    i18n: langData.data.i18n,
    devName:"",
    /**MS:00;PS:10;SS:20;IS:30; */
    devType:-1,
    /**00标准版 */
    devFirmware:-1,
    /**软件版本 */
    devSoftWare:-1,
    /**当前连接的设备的功能配置单 */
    config:null,
    build:"2020011701",
  }
})

