// pages/selectindex/selectindex.js
const dataList = [
  {
    name: '语音监控监测卡口',
    icon: '../../images/icons/voice.png',
    iconActive: '../../images/icons/voiceActive.png',
    value: 'voice',
    children: [
      {
        name: '太阳能语音宣传杆',
        disabled: false,
        value: 'solarVoice',
        url: '/pages/solarVoice/index'
      },
      {
        name: '太阳能监控监测杆',
        disabled: true,
        url: ''
      },
      {
        name: '小型火警侦察系统',
        disabled: true,
        url: ''
      },
    ]
  },
  {
    name: '森林草原火灾扑救',
    icon: '../../images/icons/forest.png',
    iconActive: '../../images/icons/forestActive.png',
    value: 'forest',
    children: [
      {
        name: '便携式接力灭火水泵',
        disabled: false,
        url: '/pages/forestindex/index'
      },
      {
        name: '电动引水泵（DN50型）',
        disabled: false,
        url: '/pages/electricWaterDiversion/index'
      },
      {
        name: '高扬程重型消防水泵',
        disabled: false,
        url: '/pages/heavyWater/index'
      },
      {
        name: '多功能森防应急救援车',
        disabled: true,
        url: ''
      },
    ]
  },
  {
    name: '抗洪抢险',
    icon: '../../images/icons/flood.png',
    iconActive: '../../images/icons/floodActive.png',
    value: 'flood',
    children: [
      {
        name: '汽（柴）油机泵',
        disabled: false,
        url: 'https://www.baidu.com'
      },
      {
        name: '便携式轴流潜水泵',
        disabled: false,
        url: ''
      },
      {
        name: '牵引式柴油机水泵机组',
        disabled: false,
        url: ''
      },
      {
        name: '浮艇泵',
        disabled: true,
        url: ''
      },
      {
        name: '电动引用岁泵（DN100型）',
        disabled: true,
        url: ''
      },
    ]
  },
  {
    name: '水域救援',
    icon: '../../images/icons/water.png',
    iconActive: '../../images/icons/waterActive.png',
    value: 'water',
    children: [
      {
        name: '舷外机',
        disabled: false,
        url: 'https://www.baidu.com'
      },
      {
        name: '舟艇拖车（牵引）',
        disabled: true,
        url: ''
      },
      {
        name: '救援船艇/橡皮艇',
        disabled: true,
        url: ''
      },
      {
        name: '水下/水面（U形）救援',
        disabled: true,
        url: ''
      },
    ]
  },
  {
    name: '地震地质灾害救援',
    icon: '../../images/icons/earthquake.png',
    iconActive: '../../images/icons/earthquakeActive.png',
    value: 'earthquake',
    children: [
      {
        name: '液压破拆工具组（液压泵）',
        disabled: true,
        url: 'https://www.baidu.com'
      },
      {
        name: '便携式防水防爆应急照明',
        disabled: true,
        url: ''
      },
      {
        name: '地质救援监控监测',
        disabled: true,
        url: ''
      },
    ]
  },
  {
    name: '通信指挥',
    icon: '../../images/icons/communication.png',
    iconActive: '../../images/icons/communicationActive.png',
    value: 'communication',
    children: [
      {
        name: '小型油动发电机（10kg）',
        disabled: false,
        url: 'https://www.baidu.com'
      },
      {
        name: '应急电源（储能）',
        disabled: true,
        url: ''
      },
      {
        name: '北斗终端/布控球/电话/腕表',
        disabled: true,
        url: ''
      },
      {
        name: '急通讯指挥车',
        disabled: true,
        url: ''
      },
    ]
  },
  {
    name: '综合保障',
    icon: '../../images/icons/general.png',
    iconActive: '../../images/icons/generalActive.png',
    value: 'general',
    children: [
      {
        name: '汽（柴）油发电机',
        disabled: false,
        url: 'https://www.baidu.com'
      },
      {
        name: '应急照明工作平台（升降灯）',
        disabled: false,
        url: ''
      },
      {
        name: '大型柴油发电机组',
        disabled: true,
        url: ''
      },
      {
        name: '便携式储能电源（1000Wh）',
        disabled: true,
        url: ''
      },
      {
        name: '12V应急启动电源',
        disabled: true,
        url: ''
      },
    ]
  },
  {
    name: '城市消防',
    icon: '../../images/icons/city.png',
    iconActive: '../../images/icons/cityActive.png',
    value: 'city',
    children: [
      {
        name: '手抬机动泵',
        disabled: true,
        url: 'https://www.baidu.com'
      },
      {
        name: '小型遥控承载车',
        disabled: true ,
        url: ''
      },
    ]
  },
]

Page({

  /**
   * 页面的初始数据
   */
  data: {
    currentType: 'voice',
    currentItem: dataList[0],
    dataList: dataList,
    disabledDialogShow: false
  },

  // entervoicemon() {
  //   console.log('123654')
  //   wx.navigateTo({
  //     url: '../index/index',
  //     success: (eer) => {
  //       console.log('跳转页面', eer)
  //     }
  //   })
  // },
  // enterforest() {
  //   console.log('xxx321')
  //   wx.navigateTo({
  //     url: '../forestindex/index',
  //     success: (eer) => {
  //       console.log('跳转页面', eer)
  //     }
  //   })
  // },
  handleTypeClick(e) {
    const { type, item } = e.currentTarget.dataset;
    this.setData({
      currentType: type,
      currentItem: item
    })
  },
  handleItemClick(e) {
    const { item } = e.currentTarget.dataset;
    if (item.disabled) {
      this.setData({
        disabledDialogShow: true
      })
      return;
    }
    if (!item.url) {
      return;
    }
    wx.navigateTo({
      url: item.url,
      success: (eer) => {
        console.log('跳转页面', eer)
      }
    })
  },
  handleDisabledDialogClose() {
    this.setData({
      disabledDialogShow: false
    })
  }
})