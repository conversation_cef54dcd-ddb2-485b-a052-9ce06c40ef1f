// pages/timer/timer.js
const app = getApp()
var user = require('../../utils/ble.js');
var devId;
var time = 0;
var sendnum = 0;
// var util = require('../../utils/util.js');
// const langData = require('../../langData.js');
// //读取默认语言
// const lg = wx.getStorageSync('language');
Page({

    /**
     * 页面的初始数据
     */
  data: {
        timeplay: "定时播放",
        intervalplay: "间隔播放",
        synplay: "同步",

        timernum: "9:00 - 18:00",
        pagenum: "23",
        repeat: "重复",
        datanum: "周一 周三",
        songnum: "歌曲名",
        controlnum: "单次",
        volumenum: "音量 40",
        relaynum: "继电器",
        setsynplay: "设置同步",
        getdoc:"获取存挡",
        storedata:"数据存储",
        deleteall:"批量删除",
        

        sData: [],
        checkvalue:[],
        revData:[],

        hiddenmodal:true,

        isSend:false,
        isLoad:false,
        isReload:false,
        isResend:false,
        isDeleteall:false,

        interval: "",      //定时器
    i18n:[],
    timingSettingEnable:true,
  },

    findIndex:function(id,buf){
        
        for(var i=0;i<buf.length;i++){
            if(buf[i].id == id){
                return i;
            }
        }
        return 0;
    },

    handleSlideDelete({ detail: { id } }) {
        let tmp = this.data.sData
        var index = this.findIndex(id,tmp)
        tmp.splice(index, 1)
        
        
        this.setData({
            sData: tmp
        })
    },
    init: function (that) {
        var time = 60;
        var interval = ""
        that.clearTimeInterval(that)
        that.setData({
            time: time,
            interval: interval,
        })
    },


    clearTimeInterval: function (that) {
        var interval = that.data.interval;
        clearInterval(interval)
    },

    restartTap: function () {
        var that = this;
        that.init(that);
        console.log("倒计时重新开始")
        that.startTap()
    },

    startTap: function () {
        var that = this;
        that.init(that);          //这步很重要，没有这步，重复点击会出现多个定时器

        var interval = setInterval(function () {
            if (that.data.isReload) {
                time++;
                if (time < 3) {
                    var buffer = [0xA4, 0x00, 0xA4];
                    user.writeData(buffer)
                }
                else {
                    that.data.isReload = false;
                    that.data.isLoad = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    if (wx.getStorageSync("language") == "zh_CN") {
                      wx.showToast({
                        title: app.globalData.i18n["control.tips.sendingFailed"],
                        image: '/images/error.png',
                        duration: 1000
                      })
                    } else {
                      wx.showModal({
                        title: 'Tips',
                        content: app.globalData.i18n["control.tips.sendingFailed"],
                        showCancel: false,
                        confirmText: "OK"
                      })
                    }
                }
            } else if (that.data.isResend){
                time++;
                if(time < 3){
                    that.synchronizeM();
                }else{
                    that.data.isResend = false;
                    that.data.isSend = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    if (wx.getStorageSync("language") == "zh_CN") {
                      wx.showToast({
                        title: app.globalData.i18n["control.tips.sendingFailed"],
                        image: '/images/error.png',
                        duration: 1000
                      })
                    } else {
                      wx.showModal({
                        title: 'Tips',
                        content: app.globalData.i18n["control.tips.sendingFailed"],
                        showCancel: false,
                        confirmText: "OK"
                      })
                    }
                }
            } else if (that.data.isDeleteall) {
                time++;
                if (time < 3) {
                    var buffer = [0xA6, 0x00, 0xA6];
                    user.writeData(buffer)
                } else {
                    that.data.isDeleteall = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    if (wx.getStorageSync("language") == "zh_CN") {
                      wx.showToast({
                        title: app.globalData.i18n["control.tips.sendingFailed"],
                        image: '/images/error.png',
                        duration: 1000
                      })
                    } else {
                      wx.showModal({
                        title: 'Tips',
                        content: app.globalData.i18n["control.tips.sendingFailed"],
                        showCancel: false,
                        confirmText: "OK"
                      })
                    }
                }
            }
            else {
                clearInterval(interval);
            }
        }, 5000)

        that.setData({
            interval: interval
        })
    },

    getdoc:function(){
        var tmp = wx.getStorageSync("sData")
        if(tmp==""){
          tmp = [];
        }
        wx.showToast({
          title: app.globalData.i18n["timer.tips.fetchstoreok"],
            duration: 1000
        })

        this.setData({
            sData: tmp,
            hiddenmodal: true
        })
    },

    storedata: function () {

        wx.setStorageSync('sData', this.data.sData)

        wx.showToast({
            title: app.globalData.i18n["timer.tips.storeok"],
            duration: 1000
        })

        this.setData({
            hiddenmodal: true
        })
    },

    deleteall: function () {
        var that = this;

        that.data.isDeleteall = true;

        var buffer = [0xA6, 0x00, 0xA6];
        user.writeData(buffer)

        time = 0;
        that.startTap()

        wx.showLoading({
          title: app.globalData.i18n["voicemanager.deleting"],
            mask: true
        })
    },

    confirm:function(){
        this.setData({
            hiddenmodal: true
        })
    },

    settimer:function(){
        this.setData({
            hiddenmodal:false
        })
    },

    swithconoff:function(e){
        // console.log(e.currentTarget.dataset.ids)
        // console.log(e.detail.value)
        this.data.sData[e.currentTarget.dataset.ids].timeenble = e.detail.value
    },

    modifytimer:function(e){
        app.globalData.id = e.currentTarget.dataset.id
        app.globalData.ismodify = true

        app.globalData.newsong[0] = this.data.sData[app.globalData.id]
        if (this.data.sData[app.globalData.id].istimeon){
            
            wx.navigateTo({
                url: '../timeplay/timeplay',
            })
        }else{
            
            wx.navigateTo({
                url: '../intervalplay/intervalplay',
            })
        }
    },

    changetimePlay:function(){
        wx.navigateTo({
            url: '../timeplay/timeplay',
        })
    },

    changeintervalplay: function () {
        wx.navigateTo({
            url: '../intervalplay/intervalplay',
        })
    },

    sequence: function (arr) {
        var t;

        for (var m = 0; m < arr.length; m++) {
            for (var n = 0; n < arr.length - m - 1; n++) {
                if ((arr[n].startTime) > (arr[n + 1].startTime)) {
                    t = arr[n + 1];
                    arr[n + 1] = arr[n];
                    arr[n] = t;
                }
            }
        }

        for(var i=0;i<arr.length;i++){
            arr[i].id = i;
        }
    },

    synchronize:function(){
        if(this.renderConfig() == false){
            return;
        }
        sendnum = 0;

        if(this.data.sData.length > 0){
            this.data.isSend = true
            this.data.isResend = true
            
            this.synchronizeM();

            time = 0;
            this.startTap();
            // var process = sendnum + 1 / (this.data.sData.length);
            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"] + "" + (sendnum + 1) + "/" + (this.data.sData.length),
              mask:true,
            })
        }else{
            wx.showToast({
              title: app.globalData.i18n["timer.tips.nodata"],
              image: '/images/error.png',
                duration: 1000
            })
        }

    },

    synchronizeM: function () {
        console.log(sendnum)
        console.log(this.data.sData[sendnum]);
        if (this.data.sData[sendnum].istimeon) {   //A2
            var buf = new Uint8Array(27)
            buf[0] = 0xA2

            buf[1] = sendnum >> 8          //ID
            buf[2] = sendnum

            if (this.data.sData[sendnum].relayON) {
                buf[3] = 1
            } else {
                buf[3] = 0
            }
            buf[4] = this.data.sData[sendnum].songnum

          if (this.data.sData[sendnum].songname != undefined && this.data.sData[sendnum].songname != ""){
              buf[5] = this.data.sData[sendnum].songname.charCodeAt(0);
              buf[6] = this.data.sData[sendnum].songname.charCodeAt(1);
              buf[7] = this.data.sData[sendnum].songname.charCodeAt(2);
              buf[8] = this.data.sData[sendnum].songname.charCodeAt(3);
              buf[9] = this.data.sData[sendnum].songname.charCodeAt(4);
              buf[10] = this.data.sData[sendnum].songname.charCodeAt(5);
              buf[11] = this.data.sData[sendnum].songname.charCodeAt(6);
              buf[12] = this.data.sData[sendnum].songname.charCodeAt(7);
            }
            if (sendnum == (this.data.sData.length - 1)){
                buf[13] = 0x60      //end write
            } else if (sendnum == 0) {
                buf[13] = 0x20      //start write
            }else{
                buf[13] = 0x40      //continue write
            }
            wx.showLoading({
                title: app.globalData.i18n["control.tips.sending"] + "" + (sendnum + 1) + "/" + (this.data.sData.length),
                mask:true,
            });
            if (this.data.sData[sendnum].allLoop) {
                if (this.data.sData[sendnum].isLoop) {
                    buf[13] += 0x10
                } else {
                    buf[13] += 0x0C
                }
            } else {
                if (this.data.sData[sendnum].isLoop) {
                    buf[13] += 0x04
                } else{
                    buf[13] += 0x00
                }
            }

            if (this.data.sData[sendnum].timeenble) {
                buf[13] += 0x02
            }

            buf[14] = this.data.sData[sendnum].volume

            var date = new Date()
            var year = date.getFullYear()
            var month = date.getMonth() + 1
            var day = date.getDate()

            buf[15] = year % 100
            buf[16] = month
            buf[17] = day

            buf[18] = this.data.sData[sendnum].week

            var str = this.data.sData[sendnum].startTime
            buf[19] = str.substring(0, 2)
            buf[20] = str.substring(3, 5)
            buf[21] = 0

            str = this.data.sData[sendnum].endTime
            buf[22] = str.substring(0, 2)
            buf[23] = str.substring(3, 5)
            buf[24] = 0

            var tem = 0;
            for (var i = 0; i < 25; i++) {
                tem += buf[i]
            }
            buf[25] = tem >> 8
            buf[26] = tem

            user.writeDataL(buf)

        } else {                              //A1
            var buf = new Uint8Array(22)
            buf[0] = 0xA1

            buf[1] = sendnum >> 8          //ID
            buf[2] = sendnum

            if (this.data.sData[sendnum].relayON) {
                buf[3] = 1
            } else {
                buf[3] = 0
            }
            buf[4] = this.data.sData[sendnum].songnum
          if (this.data.sData[sendnum].songname != undefined &&this.data.sData[sendnum].songname != "") {
              buf[5] = this.data.sData[sendnum].songname.charCodeAt(0);
              buf[6] = this.data.sData[sendnum].songname.charCodeAt(1);
              buf[7] = this.data.sData[sendnum].songname.charCodeAt(2);
              buf[8] = this.data.sData[sendnum].songname.charCodeAt(3);
              buf[9] = this.data.sData[sendnum].songname.charCodeAt(4);
              buf[10] = this.data.sData[sendnum].songname.charCodeAt(5);
              buf[11] = this.data.sData[sendnum].songname.charCodeAt(6);
              buf[12] = this.data.sData[sendnum].songname.charCodeAt(7);
            }
            if (sendnum == (this.data.sData.length - 1)) {
                buf[13] = 0x60      //end write
            } else if (sendnum == 0) {
                buf[13] = 0x20      //start write
            } else {
                buf[13] = 0x40      //continue write
            }
            wx.showLoading({
                title: app.globalData.i18n["control.tips.sending"] + "" + (sendnum + 1) + "/" + (this.data.sData.length),
                mask:true,
            });
            if (this.data.sData[sendnum].allLoop) {
                if (this.data.sData[0].isLoop) {
                    buf[13] += 0x10
                } else {
                    buf[13] += 0x0C
                }
            } else {
                if (this.data.sData[sendnum].isLoop) {
                    buf[13] += 0x04
                } else {
                    buf[13] += 0x00
                }
            }

            if (this.data.sData[sendnum].timeenble) {
                buf[13] += 0x02
            }

            buf[14] = this.data.sData[sendnum].volume

            var date = new Date()
            var year = date.getFullYear()
            var month = date.getMonth() + 1
            var day = date.getDate()

            buf[15] = year % 100
            buf[16] = month
            buf[17] = day

            buf[18] = this.data.sData[sendnum].week

            buf[19] = this.data.sData[sendnum].interval

            var tem = 0;
            for (var i = 0; i < 20; i++) {
                tem += buf[i]
            }
            buf[20] = tem >> 8
            buf[21] = tem

            user.writeDataL(buf)
        }
    },

    

    sendA4: function () {
        var that = this

        if (!that.data.isLoad) {
            
            // app.globalData.songName = [];
            // that.setData({
            //     sData: app.globalData.songName
            // })

            that.data.isLoad = true;
            that.data.isReload = true;

            var buffer = [0xA4, 0x00, 0xA4];
            user.writeData(buffer)

            time = 0;
            that.startTap()
            
            wx.showLoading({
              title: app.globalData.i18n["control.tips.loading"],
                mask: true
            })
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this;
        this.setData({
          i18n: getApp().globalData.i18n
        });
        wx.setTabBarItem({
          index: 1,
          text: this.data.i18n["navBar.Timing"],
        });
        this.setData({
          repeat: this.data.i18n["timer.repeat"],
          datanum: this.data.i18n["timer.datanum"],
          songnum: this.data.i18n["timer.songnum"],
          controlnum: this.data.i18n["timer.controlnum"],
          volumenum: this.data.i18n["timer.volumenum"],
          relaynum: this.data.i18n["timer.relaynum"],
          setsynplay: this.data.i18n["timer.setsynplay"],
          getdoc: this.data.i18n["timer.getdoc"],
          storedata: this.data.i18n["timer.storedata"],
          deleteall: this.data.i18n["timer.deleteall"],
        })
        wx.getSystemInfo({
            success: function (res) {
                that.setData({
                    windowHeight: res.windowHeight
                });
            }
        });

        app.globalData.newsong = [{
            allLoop:false,
            isLoop:false,
            songnum:1,
            songname:"",
            volume:0,
            relayON:false,
            week:0,
            startTime:"",
            endTime:"",
            weekstr: "",
            loopstr:"",
            relaystr:"",
            id:0,
            interval:0,
            istimeon:true,
            timeenble:true,
        }]
        this.renderConfig();
        this.onPullDownRefresh();
        //=======================================================
        //recieve data
        user.timerCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据t', user.hexstringFromBuffer(buf))

            if (bufView[0] == 0x7E){
                if (bufView[2] > 14){
                    that.data.revData = bufView
                    // console.log(that.data.revData)
                }
                if(that.data.isSend){
                    if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                        sendnum++;
                        that.data.isResend = false
                        that.clearTimeInterval(that)

                        if (that.data.sData.length >= (sendnum + 1)) {
                            that.synchronizeM()
                        } 
                    }

                    if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB5) && (bufView[7] == 0x7A)) {
                        wx.hideLoading();

                        that.data.isSend = false
                        that.data.isResend = false
                        that.clearTimeInterval(that)

                        wx.showToast({
                          title: app.globalData.i18n["SendOK"],
                            duration: 1000
                        })
                    }
                }

                if(that.data.isLoad){
                    if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                        that.data.isLoad = false
                        that.data.isReload = false
                        that.clearTimeInterval(that)
                        wx.hideLoading();
                    }
                }

                if (that.data.isDeleteall){
                    if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                        that.data.isDeleteall = false
                        
                        that.clearTimeInterval(that)
                        wx.hideLoading();

                        var tmp = [];
                        // wx.setStorageSync('sData', tmp)

                        wx.showToast({
                          title: app.globalData.i18n["timer.deleteallok"],
                            duration: 1000
                        })

                        this.setData({
                            sData: tmp,
                            hiddenmodal: true
                        })

                    }
                }
                
            }else{
                var bufdata = new Uint8Array(that.data.revData.length + bufView.length);
                for (var i = 0; i < that.data.revData.length;i++){
                    bufdata[i] = that.data.revData[i]
                }

                for (var i = 0; i < bufView.length; i++) {
                    bufdata[i + that.data.revData.length] = bufView[i]
                }

              console.log("分包数据合并:"+user.hexstringFromBuffer(bufdata))
                if (that.data.isLoad) {
                    if ((bufdata[0] == 0x7E) && (bufdata[1] == 0x00) && (bufdata[2] == 0x19) && (bufdata[3] == 0xB1) && (bufdata[30] == 0x7A)) {
                        console.log("B1")

                        that.data.isReload = false
                        that.clearTimeInterval(that)

                        app.globalData.newsong = [{
                            allLoop: false,
                            isLoop: false,
                            songnum: 1,
                            songname: "",
                            volume: 0,
                            relayON: false,
                            week: 0,
                            startTime: "/",
                            endTime: "",
                            weekstr: "",
                            loopstr: "",
                            relaystr: "",
                            id: 0,
                            interval: 0,
                            istimeon: true,
                            timeenble: true,
                        }]

                        app.globalData.newsong[0].istimeon = true;

                        if (bufdata[6] == 1) {
                            app.globalData.newsong[0].relayON = true
                          app.globalData.newsong[0].relaystr = that.data.i18n["timer.relaystr.on"]
                        } else {
                            app.globalData.newsong[0].relayON = false
                          app.globalData.newsong[0].relaystr = that.data.i18n["timer.relaystr.off"]
                        }

                        app.globalData.newsong[0].songnum = bufdata[7]
                        
                        var buf = new Uint8Array(8);
                        buf[0] = bufdata[8];
                        buf[1] = bufdata[9];
                        buf[2] = bufdata[10];
                        buf[3] = bufdata[11];
                        buf[4] = bufdata[12];
                        buf[5] = bufdata[13];
                        buf[6] = bufdata[14];
                        buf[7] = bufdata[15];

                        app.globalData.newsong[0].songname = String.fromCharCode.apply(null, new Uint8Array(buf));

                        var tmp = bufdata[16] & 0x1C
                        if(tmp == 0x00){
                            app.globalData.newsong[0].allLoop = false
                            app.globalData.newsong[0].isLoop = false
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Singlenoloop"];
                        } else if (tmp == 0x04) {
                            app.globalData.newsong[0].allLoop = false
                            app.globalData.newsong[0].isLoop = true
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Singleloop"];
                        } else if (tmp == 0x0C) {
                            app.globalData.newsong[0].allLoop = true
                            app.globalData.newsong[0].isLoop = false
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Allnoloop"];
                        } else if (tmp == 0x10) {
                            app.globalData.newsong[0].allLoop = true
                            app.globalData.newsong[0].isLoop = true
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Allloop"];
                        } 

                        tmp = bufdata[16] & 0x02
                        if(tmp == 0x02){
                            app.globalData.newsong[0].timeenble = true
                        }else{
                            app.globalData.newsong[0].timeenble = false
                        }

                        app.globalData.newsong[0].volume = bufdata[17];

                        app.globalData.newsong[0].week = bufdata[21];
                        app.globalData.newsong[0].weekstr = this.getweekstr(bufdata[21]);

                        var str1 = ""
                        if (bufdata[22] < 10){
                            str1 = "0" + bufdata[22]
                        }else{
                            str1 = bufdata[22].toString();
                        }

                        var str2 = ""
                        if (bufdata[23] < 10) {
                            str2 = "0" + bufdata[23]
                        } else {
                            str2 = bufdata[23].toString();
                        }

                        var str3 = ""
                        if (bufdata[25] < 10) {
                            str3 = "0" + bufdata[25]
                        } else {
                            str3 = bufdata[25].toString();
                        }

                        var str4 = ""
                        if (bufdata[26] < 10) {
                            str4 = "0" + bufdata[26]
                        } else {
                            str4 = bufdata[26].toString();
                        }

                        app.globalData.newsong[0].startTime = str1 + ":" + str2
                        app.globalData.newsong[0].endTime = str3 + ":" + str4

                        console.log(app.globalData.newsong)

                        that.addnewTimer();

                    }

                    if ((bufdata[0] == 0x7E) && (bufdata[1] == 0x00) && (bufdata[2] == 0x14) && (bufdata[3] == 0xB9) && (bufdata[25] == 0x7A)) {
                        console.log("B9")

                        that.data.isReload = false
                        that.clearTimeInterval(that)

                        app.globalData.newsong = [{
                            allLoop: false,
                            isLoop: false,
                            songnum: 1,
                            songname: "",
                            volume: 0,
                            relayON: false,
                            week: 0,
                            startTime: "/",
                            endTime: "",
                            weekstr: "",
                            loopstr: "",
                            relaystr: "",
                            id: 0,
                            interval: 0,
                            istimeon: true,
                            timeenble: true,
                        }]

                        app.globalData.newsong[0].istimeon = false;

                        if (bufdata[6] == 1) {
                            app.globalData.newsong[0].relayON = true
                          app.globalData.newsong[0].relaystr = that.data.i18n["timer.relaystr.on"]
                        } else {
                            app.globalData.newsong[0].relayON = false
                          app.globalData.newsong[0].relaystr = that.data.i18n["timer.relaystr.off"]
                        }

                        app.globalData.newsong[0].songnum = bufdata[7]

                        var buf = new Uint8Array(8);
                        buf[0] = bufdata[8];
                        buf[1] = bufdata[9];
                        buf[2] = bufdata[10];
                        buf[3] = bufdata[11];
                        buf[4] = bufdata[12];
                        buf[5] = bufdata[13];
                        buf[6] = bufdata[14];
                        buf[7] = bufdata[15];

                        app.globalData.newsong[0].songname = String.fromCharCode.apply(null, new Uint8Array(buf));

                        var tmp = bufdata[16] & 0x1C
                        if (tmp == 0x00) {
                            app.globalData.newsong[0].allLoop = false
                            app.globalData.newsong[0].isLoop = false
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Singlenoloop"];
                        } else if (tmp == 0x04) {
                            app.globalData.newsong[0].allLoop = false
                            app.globalData.newsong[0].isLoop = true
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Singleloop"];
                        } else if (tmp == 0x0C) {
                            app.globalData.newsong[0].allLoop = true
                            app.globalData.newsong[0].isLoop = false
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Allnoloop"];
                        } else if (tmp == 0x10) {
                            app.globalData.newsong[0].allLoop = true
                            app.globalData.newsong[0].isLoop = true
                          app.globalData.newsong[0].loopstr = that.data.i18n["timer.loostr.Allloop"];
                        }

                        tmp = bufdata[16] & 0x02
                        if (tmp == 0x02) {
                            app.globalData.newsong[0].timeenble = true
                        } else {
                            app.globalData.newsong[0].timeenble = false
                        }

                        app.globalData.newsong[0].volume = bufdata[17];

                        app.globalData.newsong[0].week = bufdata[21];
                        app.globalData.newsong[0].weekstr = this.getweekstr(bufdata[21]);

                        app.globalData.newsong[0].interval = bufdata[22];

                        that.addnewTimer();

                    }
                }
                
            }
        }
    },

    getweekstr:function(week){
        var str = ""
        if (week > 0) {
            if ((week & 0x01) == 0x01) {
              str += this.data.i18n["timer.weekstr.0"]
            }
            if ((week & 0x02) == 0x02) {
              str += this.data.i18n["timer.weekstr.1"]
            }
            if ((week & 0x04) == 0x04) {
              str += this.data.i18n["timer.weekstr.2"]
            }
            if ((week & 0x08) == 0x08) {
              str += this.data.i18n["timer.weekstr.3"]
            }
            if ((week & 0x10) == 0x10) {
              str += this.data.i18n["timer.weekstr.4"]
            }
            if ((week & 0x20) == 0x20) {
              str += this.data.i18n["timer.weekstr.5"]
            }
            if ((week & 0x40) == 0x40) {
              str += this.data.i18n["timer.weekstr.6"]
            }
            // console.log(str)
            str = str.substr(0, str.length - 1);
            // console.log(str)
        }
        return str;
    },

    addnewTimer:function(){
        var tmp = this.data.sData.concat(app.globalData.newsong)
        this.sequence(tmp)

        // console.log(tmp)

        this.setData({
            sData: tmp
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        if (app.globalData.isSongFlag) {
            app.globalData.isSongFlag = false;

            var tmp = this.data.sData

            if (app.globalData.ismodify) {
                app.globalData.ismodify = false
                tmp[app.globalData.id] = app.globalData.newsong[0]
                console.log(tmp)
            }
            else{
                tmp = this.data.sData.concat(app.globalData.newsong)
            }

            // console.log(tmp)

            // tmp.sort(function(a,b){
            //     return a.startTime - b.startTime
            // })

            console.log(app.globalData.newsong[0])

            this.sequence(tmp)

            this.setData({
                sData: tmp
            })

            console.log(this.data.sData)
        }

        app.globalData.ismodify = false
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.timerCallback = null;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {       
        if(this.renderConfig() == false){
            //没有设置定时功能;
            wx.stopPullDownRefresh();
            return;
        }
        var that = this

        that.sendA4();

        var tmp = [];
        this.setData({
            sData: tmp
        })

        app.globalData.newsong = [{
            allLoop: false,
            isLoop: false,
            songnum: 1,
            songname: "",
            volume: 0,
            relayON: false,
            week: 0,
            startTime: "24:00",
            endTime: "",
            weekstr: "",
            loopstr: "",
            relaystr: "",
            id: 0,
            interval: 0,
            istimeon: true,
            timeenble: true,
            txtStyle:"",
        }]

        wx.stopPullDownRefresh();
    },
    renderConfig:function(){
        var config = wx.getStorageSync("config");
        if(config){
            config = config.filter(function(obj,index){
                return obj.devId == app.globalData.devId;
            });
            if (config.length > 0) {
                config = config[config.length - 1];
                if(config.detail.timingSettingEnable == 0){
                    //没有设置定时功能;
                    // this.data.timingSettingEnable = false;
                    this.setData({
                        timingSettingEnable:false,
                    })
                }
            }
        }        
        return this.data.timingSettingEnable;
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})