/**index.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-top: 0px;
}
.input-button {
  width: 80%;
  margin-top: 100px;
}

.loren-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
}

.item-body {
  padding-left: 16px;
  padding-right: 16px;
  margin-left: 16px;
  margin-right: 16px;
  border-bottom: 1px solid grey;
}

.item-text {
  padding-top: 10px;
  padding-bottom: 10px;
  color: red;
}

.page-timr1{
    width:100%;
    display: flex;
    
    font-size: 20px;
    margin-bottom: 10px;

    position: fixed;
    bottom: 10px;

    align-items: center;

    flex-direction: column;

}

.page-btn{
    width:90%;
    color: white;
    background-color: rgb(43, 185, 43);
    display: flex;
    
    font-size: 20px;
    margin-bottom: 10px;

    justify-content: center;
}    

.page-text{
    width:100%;
    display: flex;
    justify-content: center;
    font-size: 20px;
    margin: 10px;
}

.page-text1{
    width:80%;
    display: flex;
    justify-content: center;
    flex-direction: column;
    font-size: 15px;
    margin: 10px;
}