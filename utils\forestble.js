const app = getApp()
var writecharaId = ''
var serviceId = ''
var charaId = ''
function register() {
  var date = new Date()
  var year = date.getFullYear()

  return year
}

function writeData(startPressed, stopPressed, reserved = 0) {
  // 构建数据帧
  const buffer = new ArrayBuffer(6)
  const dataView = new DataView(buffer)
  // 帧头
  dataView.setUint8(0, 0x43) // 'C'
  // 启动键标志位
  dataView.setUint8(1, startPressed ? 1 : 0)
  // 停止键标志位
  dataView.setUint8(2, stopPressed ? 1 : 0)
  // 预留位
  dataView.setUint8(3, reserved)
  // 计算CRC (前4个字节)
  const dataArray = Array.from(new Uint8Array(buffer.slice(0, 4)))
  const crc = crc16(dataArray)
  dataView.setUint8(4, crc & 0xFF) // CRC_L
  dataView.setUint8(5, (crc >> 8) & 0xFF) // CRC_H
  console.log('命令发送', buffer)
  // 发送数据
  wx.writeBLECharacteristicValue({
    deviceId: app.globalData.devId,
    serviceId: serviceId,
    characteristicId: writecharaId,
    value: buffer,
    success: function (res) {
      console.log('命令发送成功', res)
    },
    fail: function (res) {
      console.error('命令发送失败', res)
    }
  })
  return true
}

function clearAll() {
  writecharaId = '';
  serviceId = '';
  charaId = '';
}

function reconnectBle(devId) {
  console.log('要开始连接的Id==', devId)
  var that = this;
  //获取适配器
  wx.openBluetoothAdapter({
    success: function (res) {
      // success
      console.log("-----success----------");
      console.log(res);
      //开始搜索
      wx.startBluetoothDevicesDiscovery({
        services: ["0000ffe0-0000-1000-8000-00805f9b34fb"],
        allowDuplicatesKey: true,
        success: function (res) {
          // success
          console.log("-----startBluetoothDevicesDiscovery--success----------", res);

          wx.onBluetoothDeviceFound(function (obj) {
            console.log(obj);
            if (obj.devices[0].name) {
              obj.devices.map(dev => {
                if (dev.deviceId == devId) {
                  that.connectBle(devId)
                }
              })
            }

          })
        },
        fail: function (res) {
          // fail
          console.log(res);
        },
        complete: function (res) {
          // complete
          console.log(res);
        }
      })
    },
    fail: function (res) {

      wx.showModal({
        title: '提示',
        content: "请检测手机蓝牙是否打开",
        showCancel: false
      })
    },
  })
}

function connectBle(devId) {
  // 显示加载状态
  wx.showLoading({
    title: '连接中...',
    mask: true
  });
  var that = this
  // 先停止搜索
  // wx.stopBluetoothDevicesDiscovery();
  //点击连接设备
  wx.createBLEConnection({
    deviceId: devId,
    success: function (creatres) {
      wx.hideLoading();
      console.log('开始连接设备', creatres)
      // 监听蓝牙连接状态改变
      wx.onBLEConnectionStateChange(function (connectState) {
        console.log(`Bluetooth connection state has changed: ${connectState.deviceId}, connected: ${connectState.connected}`);
        if (that.userBleConnectState) {
          that.userBleConnectState(connectState)
        }
      });
      //获取服务
      wx.getBLEDeviceServices({
        deviceId: devId,
        success: function (getres) {
          // sendId = devId;
          console.log('连接设备获取服务', getres);
          //console.log(sendId);
          serviceId = getres.services[0].uuid
          // if () {

          // }
          //获取特征值
          wx.getBLEDeviceCharacteristics({
            deviceId: devId,
            serviceId: serviceId,

            success: function (charactres) {
              console.log('device getBLEDeviceCharacteristics:', charactres.characteristics)
              console.log('获取特征值', charactres)
              if (that.userBleConnected) {
                that.userBleConnected(charactres)
              }
              charactres.characteristics.map((item, index) => {
                if (item.properties.notify) {
                  charaId = item.uuid
                } else if (item.properties.write) {
                  writecharaId = item.uuid
                }
              })
              that.listener(devId, serviceId, charaId)
            }
          })
        }
      })
    },
    fail: res => {
      //连接失败
      console.log(res)
      if (that.userBleConnectFailed) {
        that.userBleConnectFailed(res)
      }

    }
  })
}

function listener(devId, serviceId, charaId) {
  var that = this
  wx.notifyBLECharacteristicValueChange({
    state: true,
    deviceId: devId,
    serviceId: serviceId,
    characteristicId: charaId,
    success: function (res) {
      console.log(res)
    },
    fail(res) {
      console.log(res);
    }
  })
  wx.onBLECharacteristicValueChange(function (res) {
    const value = res.value;
    const dataView = new DataView(value);
    // 打印原始数据用于调试
    console.log('原始数据:', Array.from(new Uint8Array(value)), Array.from(new Uint8Array(value)).length)
    console.log('收到数据', dataView)
    if (Array.from(new Uint8Array(value)).length >= 15) {
      // 尝试小端序读取CRC
      const receivedCrc = dataView.getUint16(13, true); // true表示小端序
      // 计算前13字节的CRC
      const dataArray = Array.from(new Uint8Array(value, 0, 13));
      const calculatedCrc = crc16(dataArray);
      console.log('CRC校验', receivedCrc, calculatedCrc);
      if (receivedCrc !== calculatedCrc) {
        console.error('CRC校验失败');
        // return;
      }

      // 解析数据
      const deviceId = (dataView.getUint8(1) << 8) | dataView.getUint8(2);

      // 解析温度 (bit15为符号位，数据放大10倍)
      const tempRaw = (dataView.getUint8(3) << 8) | dataView.getUint8(4);
      const isNegative = (tempRaw & 0x8000) !== 0;
      const temperature = (isNegative ? -1 : 1) * (tempRaw & 0x7FFF) / 10;

      // 解析水压 (放大100倍)
      const pressure = ((dataView.getUint8(5) << 8) | dataView.getUint8(6)) / 100;

      // 解析工作时长 (放大10倍)
      const workingHours = ((dataView.getUint8(7) << 8) | dataView.getUint8(8)) / 10;

      // 解析转速
      const speed = (dataView.getUint8(9) << 8) | dataView.getUint8(10);

      // 解析遥控状态
      const remoteControlStatus = dataView.getUint8(11) === 1;

      // 预留位
      const reserved = dataView.getUint8(12);

      // 处理解析后的数据
      console.log('处理解析后的数据', {
        deviceId,
        temperature,
        pressure,
        workingHours,
        speed,
        remoteControlStatus,
        reserved
      });

      if (that.userGetdataCallback) {
        that.userGetdataCallback({
          deviceId,
          temperature,
          pressure,
          workingHours,
          speed,
          remoteControlStatus,
          reserved
        })
      }
    }
  })

  app.globalData.devId = devId
  // wx.hideLoading()
}

// CRC16计算函数示例 (需要根据文档中的具体算法实现)
function crc16(dataArray) {
  let crc = 0xFFFF
  for (let i = 0; i < dataArray.length; i++) {
    crc ^= dataArray[i]
    for (let j = 0; j < 8; j++) {
      if (crc & 0x0001) {
        crc >>= 1
        crc ^= 0xA001
      } else {
        crc >>= 1
      }
    }
  }
  return crc
}

function procData(buf) {
  var i, length, num;
  length = buf.length;

  num = 0;
  var buf1 = new Uint8Array(length * 2);
  var buf0 = new Uint8Array(num + 4);
  buf0[0] = 0x7E;
  buf0[1] = (num - 2) >> 8
  buf0[2] = num - 2;

  for (var k = 0; k < num; k++) {
    buf0[k + 3] = buf1[k];
  }

  buf0[num + 3] = 0x7A;

  return buf0;
}

function checkData(buf) {
  var num = 1;
  var k = 1;
  var buf0 = new Uint8Array(buf.length);
  buf0[0] = buf[0];
  while (1) {
    if (buf[k] == 0x70) {
      buf0[num] = buf[k] + buf[k + 1];
      k += 2;
      num++;
    } else {
      buf0[num] = buf[k];
      k++;
      num++;
    }
    if (k >= (buf.length - 1)) break;
  }
  buf0[num] = buf[k];
  num++;

  var buf2 = new Uint8Array(num);
  for (var i = 0; i < num; i++) {
    buf2[i] = buf0[i];
  }

  return buf2;
}

function bufferFormArray(array) {
  return new Uint8Array(array).buffer;
}
function arrayFromBuffer(arrayBuffer) {
  return Array.prototype.slice.call(new Uint8Array(arrayBuffer));
}
function hexstringFromBuffer(buffer) { // buffer is an ArrayBuffer
  return Array.prototype.map.call(new Uint8Array(buffer), x => ('00' + x.toString(16)).slice(-2)).join('');
}
function bufferFormHexstring(hex) {
  if (hex.trim().length == 0) {
    return new ArrayBuffer();
  }
  var typedArray = new Uint8Array(hex.match(/[\da-f]{2}/gi).map(function (h) {
    return parseInt(h, 16)
  }))
  return typedArray.buffer;
}

module.exports = {
  writeData: writeData,
  register: register,
  clearAll: clearAll,
  reconnectBle: reconnectBle,
  connectBle: connectBle,
  listener: listener,
  procData: procData,
  checkData: checkData,
  bufferFormArray: bufferFormArray,
  arrayFromBuffer: arrayFromBuffer,
  hexstringFromBuffer: hexstringFromBuffer,
  bufferFormHexstring: bufferFormHexstring,
}