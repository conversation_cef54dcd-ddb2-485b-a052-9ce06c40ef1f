<!--pages/control/control.wxml-->
<!-- <modal hidden="{{hiddenmodal}}" title="蓝牙配对密码" confirm-text="连接" cancel-text="取消" bindcancel="cancel" bindconfirm="confirm"> -->

<view  hidden="{{hiddenmodal}}" class="zan-dialog--show">
  <view class="zan-dialog__mask" bindtap="toggleDialog" />
  <view class="zan-dialog__container">
    <view style='text-align:center; height: 100rpx;line-height: 100rpx;'>{{i18n['control.input.password']}}</view>
    <input type='text' class="viewset2" maxlength="6" placeholder="{{i18n['control.tips.password']}}" bindinput="getInput" focus="{{inputShowed}}" cursor-spacing='{{space}}'/> 
    <view style="position:absolute; bottom:0;background:rgba(7,17,27,0.13);width:1rpx;height:100rpx;left:325rpx;z-index: 12;"></view>
    <view class='modal-btn-wrapper' >
      <button class="cancel-btn" bindtap="cancel">{{i18n['cancel']}}</button>
      <button class="confirm-btn" style="right:0;" bindtap = "confirm">{{i18n['control.input.connect']}}</button>
    </view>
  </view>
</view>

<view class="page-timr2">
    <view class="page-timr4">{{inductionnum}}</view>
    <switch checked="true" class="page-timr6" bindchange="changOnOff"></switch>
</view>

<view class="page-timr1">{{songnum}}</view>

<view class="page-timr3">
    <view  catchtouchstart="{{isON?'touchstartlast':''}}" catchtouchend="{{isON?'touchendlast':''}}">
        <image class="btnImg" src="{{lasturl}}" ></image>
    </view>

    <view bindtap="{{isON?'changeplay':''}}" >
        <image wx:if="{{isPlay}}" class="btnImg1" src="/images/pause.png"></image>
        <image wx:else class="btnImg1" src="/images/play.png"></image>
    </view>

    <view  catchtouchstart="{{isON?'touchstartnext':''}}" catchtouchend="{{isON?'touchendnext':''}}" >
        <image class="btnImg" src="{{nexturl}}" ></image>
    </view>
</view>

<view class="page-timr3">
    <view bindtap="setmute">
        <image class="btnImg2" src="/images/mute.png" ></image>
    </view>
    <view>
        <slider disabled="{{!isON}}" bindchange="volchange" class="sliderset" show-value="true" max="30" value="{{volumevalue}}"></slider>
    </view>
    <view>
        <image wx:if="{{volumevalue > 20}}" class="btnImg2" src="/images/voice-3.png"></image>
        <image wx:elif="{{volumevalue > 10}}" class="btnImg2" src="/images/voice-2.png"></image>
        <image wx:elif="{{volumevalue > 0}}" class="btnImg2" src="/images/voice-1.png"></image>
        <image wx:else class="btnImg2" src="/images/voice-0.png"></image>
    </view>
</view>

<view class="page-timr5" >
    <view class="viewset bg-gray">
        <view class="viewset1 bg-view" hover-class="bg-gray" bindtap="{{isON?'triggerpage':''}}">
            <image class="btnImg3" src="/images/trigger.png"></image>
            <text>{{trigger}}</text>
        </view>
        
    </view>
    <view class="viewset bg-gray">
        <view class="viewset1 bg-view" hover-class="bg-gray" bindtap="{{isON?'playpage':''}}">
            <image class="btnImg3" src="/images/playmode.png"></image>
            <text>{{playmode}}</text>
        </view>
        
    </view>
    <view class="viewset bg-gray">
        <view class="viewset1 bg-view" hover-class="bg-gray" bindtap="{{isON?'changeiamge':''}}">
            <image wx:if="{{isShow}}" class="btnImg3" src="/images/recording.png" ></image>
            <image wx:else class="btnImg3" src='/images/record.png' ></image>
            <text wx:if="{{isShow}}">{{recording}}</text>
            <text wx:else>{{record}}</text>
        </view>
    </view>
</view>

<view class="page-timr7" >
    <view class="viewset bg-gray">
        <view class="viewset1 bg-view" hover-class="bg-gray" bindtap="{{isON?'voicemanage':''}}">
            <image class="btnImg3" src="/images/voice.png"></image>
            <text>{{voice}}</text>
        </view>
        
    </view>
    <view class="viewset bg-gray">
        <view class="viewset1 bg-view" hover-class="bg-gray"  bindtap="{{isON?'voicechange':''}}">
            <image class="btnImg3" src="/images/doc.png"></image>
            <text>{{doc}}</text>
        </view>
        
    </view>
    <view class="viewset bg-gray">
        <view class="viewset1 bg-view" hover-class="bg-gray" bindtap="{{isON?'customize':''}}">
            <image class="btnImg3" src="/images/customize.png"></image>
            <text>{{customize}}</text>
        </view>
        
    </view>
</view>




