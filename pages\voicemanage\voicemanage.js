// pages/voicemanage/voicemanage.js
const app = getApp()
var user = require('../../utils/ble.js');
var time = 0;
var playbuffer = new Uint8Array(13);
Page({

    /**
     * 页面的初始数据
     */
    data: {
        voicename: "test",
        play: "play",
        playurl: "/images/playmini.png",
        ldata: ["00000001", "00000002", "00000003", "00000004", "00000005", "00000006"],
        sData: [],
        lastsong: "删除最后一首",
        allsong: "全部删除",

        isVoice: false,
        isDellast: false,
        isDelall: false,
        isPlay: false,
        isResend: false,
        isDelete:false,
        interval: "",      //定时器
        i18n:[],
    },

    findIndex: function (id, buf) {

        for (var i = 0; i < buf.length; i++) {
            if (buf[i].id == id) {
                return i;
            }
        }
        return -1;
    },
    sortSong:function(){
        // app.globalData.songName
        var array = app.globalData.songName;
        array.sort((a,b)=>{
            return a.id - b.id;
        });
    },
    handleSlideDelete({ detail: { id } }) {
        //功能未开放
        return;
        let tmp = this.data.sData
        var index = this.findIndex(id, tmp)

        this.voicedelete(index);

        //todo:应该在收到结束后,再处理.
        //删除不成功的原因,有可能是id变了,与歌曲名称对应不上导致的.经过确认,删除指定id的功能未实现.暂时不理.
        //删除后,文件名称会按序号重新命名.
        tmp.splice(index, 1)

        this.setData({
            sData: tmp
        })
    },

    voicedelete:function(index){
        var that = this
        that.data.isDelete = true;
        
        // var index = parseInt(e.currentTarget.dataset.index)

        console.log(index)
        if(index >= this.data.sData.length || index < 0){
            return;
        }
        wx.showLoading({
            title: app.globalData.i18n["control.tips.sending"],
              mask: true
        })
        var str = this.data.sData[index].name
        var name = new Uint8Array(8);
        for (var i = 0; i < 8; i++) {
            name[i] = str.charCodeAt(i);
        }

        // var buffer = new Uint8Array(13);
        playbuffer[0] = 0xC1;
        playbuffer[1] = 0x02;
        playbuffer[2] = index + 1;
        playbuffer[3] = name[0];
        playbuffer[4] = name[1];
        playbuffer[5] = name[2];
        playbuffer[6] = name[3];
        playbuffer[7] = name[4];//70?
        playbuffer[8] = name[5];
        playbuffer[9] = name[6];
        playbuffer[10] = name[7];
        var num = 0;
        for (var i = 0; i < 11; i++) {
            num += playbuffer[i];
        }
        playbuffer[11] = num >> 8;
        playbuffer[12] = num;
        user.writeData(playbuffer)

        time = 0;
        that.startTap();
        // var interval = setInterval(function () {
        //     if (that.data.isPlay) {
        //         time++;
        //         if (time < 3) {
        //             user.writeData(buffer)
        //         }
        //         else {
        //             that.data.isPlay = false
        //             clearInterval(interval);
        //             wx.hideLoading();
        //             wx.showToast({
        //                 title: "Play error!",
        //                 duration: 1000
        //             })
        //         }
        //     }
        //     else {
        //         clearInterval(interval);
        //     }

        // }, 5000)


    },
    //初始化定时器?
    init: function (that) {
        var time = 60;
        var interval = ""
        that.clearTimeInterval(that)
        that.setData({
            time: time,
            interval: interval,
        })
    },


    clearTimeInterval: function (that) {
        var interval = that.data.interval;
        clearInterval(interval)
    },

    restartTap: function () {
        var that = this;
        that.init(that);
        console.log("倒计时重新开始")
        that.startTap()
    },

    startTap: function () {
        var that = this;
        that.init(that);          //这步很重要，没有这步，重复点击会出现多个定时器

        var interval = setInterval(function () {
            if (that.data.isResend) {
                time++;
                if (time < 3) {
                    var buffer = [0xAA, 0x00, 0xAA];
                    user.writeData(buffer)
                }
                else {
                    that.data.isResend = false;
                    that.data.isVoice = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.voicerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            } else if (that.data.isPlay) {
                time++;
                if (time < 3) {
                    console.log(playbuffer)
                    user.writeData(playbuffer)
                }
                else {
                    that.data.isPlay = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.playerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            } else if (that.data.isDelete) {
                time++;
                if (time < 3) {
                    console.log(playbuffer)
                    user.writeData(playbuffer)
                }
                else {
                    that.data.isDelete = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.deleteerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }
        }, 5000)

        that.setData({
            interval: interval
        })
    },

    // onPullDownRefresh: function () {
    //     console.log("pulldown")
    //     wx.stopPullDownRefresh();
    // },

    dellast: function () {
        var that = this
        that.data.isDellast = true;

        var buffer = [0xAD, 0x00, 0xAD];
        user.writeData(buffer)

        that.data.isLast = true;

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isDellast) {
                time++;
                if (time < 3) {

                    user.writeData(buffer)
                }
                else {
                    that.data.isDellast = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.deleteerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["voicemanager.deleting"],
            mask: true
        })
    },

    delall: function () {
        var that = this
        that.data.isDelall = true;

        var buffer = [0xAE, 0x00, 0xAE];
        user.writeData(buffer)

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isDelall) {
                time++;
                if (time < 3) {

                    user.writeData(buffer)
                }
                else {
                    that.data.isDelall = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.deleteerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["voicemanager.deleting"],
            mask: true
        })
    },

    playsong: function (e) {
        var that = this
        that.data.isPlay = true;

        var index = parseInt(e.currentTarget.dataset.index)

        console.log(index)

        var str = this.data.sData[index].name
        var name = new Uint8Array(8);
        for (var i = 0; i < 8; i++) {
            name[i] = str.charCodeAt(i);
        }

        // var buffer = new Uint8Array(13);
        playbuffer[0] = 0xC1;
        playbuffer[1] = 0x01;
        playbuffer[2] = index + 1;
        playbuffer[3] = name[0];
        playbuffer[4] = name[1];
        playbuffer[5] = name[2];
        playbuffer[6] = name[3];
        playbuffer[7] = name[4];//70?
        playbuffer[8] = name[5];
        playbuffer[9] = name[6];
        playbuffer[10] = name[7];
        var num = 0;
        for (var i = 0; i < 11; i++) {
            num += playbuffer[i];
        }
        playbuffer[11] = num >> 8;
        playbuffer[12] = num;
        user.writeData(playbuffer)

        time = 0;
        that.startTap();
        // var interval = setInterval(function () {
        //     if (that.data.isPlay) {
        //         time++;
        //         if (time < 3) {
        //             user.writeData(buffer)
        //         }
        //         else {
        //             that.data.isPlay = false
        //             clearInterval(interval);
        //             wx.hideLoading();
        //             wx.showToast({
        //                 title: "Play error!",
        //                 duration: 1000
        //             })
        //         }
        //     }
        //     else {
        //         clearInterval(interval);
        //     }

        // }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })
    },

    sendAA: function () {
        var that = this

        if (!that.data.isVoice) {
            // console.log(!that.data.isVoice)
            // console.log(that.data.isVoice)
            wx.showLoading({
                title: app.globalData.i18n["control.tips.loading"],
                  mask: true
              })
            app.globalData.songName = [];
            that.setData({
                sData: app.globalData.songName
            })
            //标志正在查询语音列表
            that.data.isVoice = true;
            //标志正在重发查询语音列表
            that.data.isResend = true

            var buffer = [0xAA, 0x00, 0xAA];
            user.writeData(buffer)

            time = 0;
            that.startTap()
            // var interval = setInterval(function () {
            //     if (that.data.isResend) {
            //         time++;
            //         if (time < 3) {

            //             user.writeData(buffer)
            //         }
            //         else {
            //             that.data.isResend = false;
            //             clearInterval(interval);
            //             wx.hideLoading();
            //             wx.showToast({
            //                 title: "voice error!",
            //                 duration: 1000
            //             })
            //         }
            //     }
            //     else {
            //         clearInterval(interval);
            //     }

            // }, 5000)


        }


    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this
        this.setData({
          i18n:app.globalData.i18n,
          lastsong: app.globalData.i18n["voicemanager.lastsong"],
          allsong: app.globalData.i18n["voicemanager.allsong"],
        })
        that.sendAA();

        app.globalData.songName = [];
        that.setData({
            sData: app.globalData.songName
        })
        //=======================================================
        //recieve data
        user.voiceCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据v', user.hexstringFromBuffer(buf))

            // if (that.data.isVoice) {
            // console.log("isVoice")
            if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x09) && (bufView[3] == 0xB2) && (bufView[14] == 0x7A)) {
                that.data.isResend = false
                that.clearTimeInterval(that)
                // wx.hideLoading();

                var buf = new Uint8Array(8);
                buf[0] = bufView[4];
                buf[1] = bufView[5];
                buf[2] = bufView[6];
                buf[3] = bufView[7];
                buf[4] = bufView[8];
                buf[5] = bufView[9];
                buf[6] = bufView[10];
                buf[7] = bufView[11];

                // //var str = String.fromCharCode.apply(null, new Uint8Array(buf));
                // //歌曲名称第一首即清空了再加,但是对于名称不是按序号来的,不起作用,有中文的话,会崩溃;
                // var firstElm = String.fromCharCode.apply(null, new Uint8Array(buf));
                // if(parseInt(firstElm) == 1){
                //     app.globalData.songName = [];
                // }
                // //歌曲总数添加;
                // var num = app.globalData.songName.length
                // //名称是按序号来的,不可改.有些产品的名称不是按序号来的.这个做法应该取消;
                // var str = (Array(6).join(0) + (num+1)).slice(-6);
                // var newsong = [{
                //     id:num,
                //     name:str,
                // }]
                // app.globalData.songName = app.globalData.songName.concat(newsong);

                var currSongName = String.fromCharCode.apply(null, new Uint8Array(buf));
                // var currSongName = decodeURIComponent(escape((currSongName)));下位机不支持读取中文.
                var finIndex = app.globalData.songName.findIndex(function(value,index){
                    return value.name == currSongName;
                });
                if(finIndex < 0){
                    var num = app.globalData.songName.length
                    var newsong = [{
                        id:num,
                        name:currSongName,
                    }]
                    app.globalData.songName = app.globalData.songName.concat(newsong);
                }
                //排序;
                that.sortSong();
                console.log(app.globalData.songName)

                that.setData({
                    sData: app.globalData.songName
                })
            }
            // }

            // if (that.data.isVoice) {
            if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xBC) && (bufView[7] == 0x7A)) {
                console.log("isVoice")
                that.data.isVoice = false
                that.data.isResend = false
                that.clearTimeInterval(that)
                wx.hideLoading();
            }
            // }

            if (that.data.isDellast) {
                console.log("isDellast")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[4] == 0xAD) && (bufView[7] == 0x7A)) {
                    console.log("isDellast")
                    that.data.isDellast = false
                    //重新获取文件列表,没有必要,直接删除数组最后一条数据即可.
                    // that.sendAA();
                    // app.globalData.songName = [];
                    // that.setData({
                    //     sData: app.globalData.songName
                    // })
                    app.globalData.songName.splice(app.globalData.songName.length - 1, 1);
                    that.setData({
                        sData: app.globalData.songName
                    })
                }
            }

            if (that.data.isDelall) {
                console.log("isDelall")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[4] == 0xAE) && (bufView[7] == 0x7A)) {
                    that.data.isDelall = false

                    that.sendAA();

                    app.globalData.songName = [];
                    that.setData({
                        sData: app.globalData.songName
                    })
                }
            }

            if (that.data.isPlay) {
                console.log("isPlay")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[4] == 0xC1) && (bufView[7] == 0x7A)) {
                    that.data.isPlay = false

                    that.clearTimeInterval(that)

                    wx.hideLoading();

                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.playing"],
                        duration: 1000
                    })
                }
            }

            if (that.data.isDelete) {
                console.log("isDelete")
                //todo:删除指定id歌曲成功,需要重新排序和命名;目前文件名称会按顺序重新命名.
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[4] == 0xC1) && (bufView[7] == 0x7A)) {
                    that.data.isDelete = false
                    that.clearTimeInterval(that)

                    wx.hideLoading();

                    wx.showToast({
                      title: app.globalData.i18n["voicemanager.deleteok"],
                        duration:1000
                    })
                }
            }

        }
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.voiceCallback = null;
      clearInterval(this.data.interval);
      wx.hideLoading({
        complete: (res) => {},
      })
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
        var that = this

        that.sendAA();


        wx.stopPullDownRefresh();
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})