<!--pages/tts/tts.wxml-->
<!-- <web-view src="https://www.data-baker.com/tts.html"></web-view> -->

<view class="container" style="padding:0;align-items:stretch;" ontouchmove="">
<label bindtap="navback" class="weui-msg__link" style="margin-left:20px;display:{{(shareOpen == true && downloadurl!='')?'inline-block':'none'}}">点击查看文件列表</label>
  <textarea placeholder="{{i18n['tts.tip.inputpls']}}" auto-focus="true" maxlength="{{ttsConfig[engineIndex == null?0:engineIndex].maxLength}}" style="box-sizing:border-box;width:calc( 100% - (20px) );margin:10px;" bindinput="onTTSInput" value="{{ttsConfig[engineIndex].text}}"></textarea>
  <view style="margin-right:20px;" class="weui-textarea-counter">{{i18n['tts.txt.txtcount']}}:{{ttsConfig[engineIndex].text.length + "/" + ttsConfig[engineIndex == null?0:engineIndex].maxLength}}</view>
  <view class="pickerRow" style="">
    <view>{{i18n['tts.txt.engine']}}:</view>
    <picker bindchange="platformPickerChanged" value="{{engineIndex}}" range="{{ttsConfig}}" range-key='name' bindcancel="platformPickerCancel" class="pickerView">
      <view class="pickerTitle">
        {{ttsConfig[engineIndex == null?0:engineIndex].name}}
      </view>
    </picker>
    <view class="pickerImage">v</view>
  </view>
  <view class="pickerRow">
    <view>{{i18n['tts.txt.speaker']}}:</view>
    <picker bindchange="personPickerChanged" value="{{personIndex}}" range="{{ttsConfig[engineIndex].person}}" range-key='name' bindcancel="personPickerCancel" class="pickerView">
      <view class="pickerTitle">
        {{ttsConfig[engineIndex].person[personIndex == null?0:personIndex].name}}
      </view>
    </picker>
    <view class="pickerImage">w</view>
  </view>
  <view class="pickerRow" hidden="{{ttsConfig[engineIndex].volume.hidden}}">
    <view class="pickerLabel">{{i18n['tts.txt.volume']}}:</view>
    <slider class="pickerView" min="{{ttsConfig[engineIndex].volume.min}}" max="{{ttsConfig[engineIndex].volume.max}}" step="{{ttsConfig[engineIndex].volume.step}}" value="{{ttsConfig[engineIndex].volume.value}}" bindchange="onVolumeSliderChanged" show-value>
    </slider>
  </view>
  <view class="pickerRow" hidden="{{ttsConfig[engineIndex].speed.hidden}}">
    <view class="pickerLabel">{{i18n['tts.txt.speed']}}:</view>
    <slider class="pickerView" min="{{ttsConfig[engineIndex].speed.min}}" max="{{ttsConfig[engineIndex].speed.max}}" step="{{ttsConfig[engineIndex].speed.step}}" value="{{ttsConfig[engineIndex].speed.value}}" bindchange="onSpeedSliderChanged" show-value>
    </slider>
  </view>
  <view class="pickerRow" hidden="{{ttsConfig[engineIndex].anchor.hidden}}">
    <view class="pickerLabel">{{i18n['tts.txt.pitch']}}:</view>
    <slider class="pickerView" min="{{ttsConfig[engineIndex].anchor.min}}" max="{{ttsConfig[engineIndex].anchor.max}}" step="{{ttsConfig[engineIndex].anchor.step}}" value="{{ttsConfig[engineIndex].anchor.value}}" bindchange="onPitchSliderChanged" show-value>
    </slider>
  </view>
  <button bindtap="onTTSPlayButtonClicked">{{i18n['tts.txt.ttsplay']}}</button>  
  <view hidden="{{downloadurl==''}}" style="width:100%;word-wrap:break-word;overflow: hidden;">
    <view bindlongtap='onCopyClicked' data-message='{{downloadurl}}'>{{i18n['tts.tip.copy']}}</view>
    <label bindlongtap='onCopyClicked' data-message='{{downloadurl}}' style="color:#586c94">{{downloadurl}}</label>
  </view>
  
   <view hidden="{{errMsg==''}}" style="width:100%;word-wrap:break-word;overflow: hidden;">
      <label bindlongtap='onCopyClicked' data-message='{{errMsg}}' style="color:red">{{errMsg}}</label>
  </view>
  
</view>