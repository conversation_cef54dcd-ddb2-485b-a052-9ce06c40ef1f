<modal hidden="{{hiddenmodal}}" no-cancel="true" confirm-text="{{i18n['cancel']}}" bindconfirm="confirm">
	<view class="viewset4" bindtap="getdoc">{{getdoc}}</view>
	<view class="divLine"></view>
	<view class="viewset3" bindtap="storedata">{{storedata}}</view>
	<view class="divLine"></view>
	<view class="viewset4" bindtap="deleteall">{{deleteall}}</view>
</modal>
<view style="width:100%;height:100%;" hidden="{{timingSettingEnable}}">
    <text  style="text-align: center;color:red;display:block">{{i18n["timing.tips.disable"]}}</text>
</view>

<view class="page-timr-2" hidden="{{!timingSettingEnable}}">

	<view style="width:100%;background-color:#f0f0f0;position:fixed;top:0;z-index: 2;">
		<view class="page">
			<view class="page1" bindtap="changetimePlay">
				<image class="btnImg" src="/images/create.png"></image>
				<view>{{i18n["timer.timingPlay"]}}</view>
			</view>
			<view class="page1" bindtap="changeintervalplay">
				<image class="btnImg" src="/images/create.png"></image>
				<view>{{i18n["timer.interPlay"]}}</view>
			</view>
			<!-- <view class="page1"  bindtap="changeColor">
        <image class="btnImg1" src="/images/synchronize.png"></image>
        <view>{{synplay}}</view>
    </view> -->
			<view class="page3" bindtap="settimer">
				<image class="btnImg" src="/images/more.png"></image>
			</view>
		</view>
	</view>


	<view>
		<view style="width:100%;height:50px;"></view>

		<block wx:for="{{sData}}" wx:key="{{item.id}}">
			<slide-delete pid="{{item.id}}" bindaction="handleSlideDelete">

				<view class="page-timer bg-gray" data-index="{{index}}">
					<view class="page-timr2">
						<view wx:if="{{item.istimeon}}" class="page-timr1">{{item.startTime}} - {{item.endTime}}</view>
						<view wx:else class="page-timr1">{{i18n["timer.interval"]}} {{item.interval}} {{i18n["timer.min"]}}</view>
						<view bindtap="modifytimer" data-id="{{index}}" style="font-size:20px;color:#f00000">{{i18n["timer.edit"]}}</view>
						<view class="page-timr3 bg-green">{{index}}</view>
					</view>

					<view class="page-timr2">
						<view class="page-timr4">{{repeat}}</view>
						<view class="page-timr5">{{item.weekstr}}</view>
					</view>

					<view class="page-timr2">
						<view class="page-timr4">{{item.songname}}</view>
						<view class="page-timr4">{{item.loopstr}}</view>
						<view class="page-timr4">{{i18n["timer.volume"]}}{{item.volume}}</view>
						<switch bindchange="swithconoff" checked="{{item.timeenble}}" data-ids="{{index}}"></switch>

					</view>

					<view class="page-timr2">
						<view class="page-timr4">{{relaynum}}</view>
						<view class="page-timr7">{{item.relaystr}}</view>

					</view>
				</view>
			</slide-delete>
		</block>
		<view style="width:100%;height:50px;"></view>
	</view>

	<view class="bottom" style="width:100%;background-color:#f0f0f0;position:fixed;bottom:0;z-index: 2;">
		<view class="page2">

			<view class="page1" bindtap="synchronize">
				<image class="btnImg1" src="/images/synchronize.png"></image>
				<view>{{i18n["timer.dataSync"]}}</view>
			</view>

		</view>
	</view>
</view>