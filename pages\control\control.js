// pages/control/control.js
const app = getApp()
var user = require('../../utils/ble.js');
var time = 0;
// var util = require('../../utils/util.js');
// const langData = require('../../langData.js');
// //读取默认语言
// const lg = wx.getStorageSync('language');
var eventChannel;
Page({
  isQueryingRecordingState:false,
    /**
     * 页面的初始数据
     */
  data: {
        lasturl: "/images/lastsong.png",
        nexturl: "/images/nextsong.png",
        i18n:[],
        inductionnum: "感应开关",
        songnum: "歌名",
        trigger: "触发方式",
        playmode: "播放模式",
        record: "录音更换",
        voice: "设备语音",
        doc: "语音更换",
        customize: "自定义",

        volume: 0,

        isShow: false,
        recording: "正在录音",
        isPlay: false,
        isMute: false,
        // isDisabled: false,
        isON: true,
        isStartRecord: false,
        isStopRecord: false,
        isStartPlay: false,
        isStopPlay: false,

        isLogin: false,
        isTime: false,
        isLast: false,
        isNext: false,
        isSetmute:false,
        isSetvolume:false,
        isPlaymode: false,

        songnum: "song",
        volumevalue: 10,
        password:"",
        hiddenmodal: true,
        devstored:[],
        space: 150,

        award_result_goods: [
            {
                awardname: 1,
                award: '',
                time: '2018-8-27',
                awardimg: ''
            },
            {
                awardname: 1,
                award: '',
                time: '2018-8-27',
                awardimg: ''
            }
        ],
        award_result: [],

        inputShowed: false,

        initTimeoutCode:0,
        currentPage:"",
    },

    changOnOff: function (e) {
        this.setData({
            isON: e.detail.value,
            // isDisabled: !e.detail.value,
        })
    },

    getInput: function (e) {//方法1
        this.setData({
            password: e.detail.value
        });
    },

    //取消按钮
    cancel: function (e) {
        // wx.showToast({
        //     title: "请输入6位密码",
        //     duration: 1000
        // })
      wx.stopBluetoothDevicesDiscovery({

      })
      clearTimeout(this.data.initTimeoutCode);
      wx.closeBluetoothAdapter({
        complete: function (res) {
          if (getCurrentPages().reverse()[0].route != "pages/index/index"){
            wx.hideLoading();            
            wx.reLaunch({
              url: '../index/index',
            });
          }
        },
      });
    },
    showSendingFailed:function(){
      if (wx.getStorageSync("language") == "zh_CN") {
        wx.showToast({
          title: app.globalData.i18n["control.tips.sendingFailed"],
          image: '/images/error.png',
          duration: 1000
        })
      } else {
        wx.showModal({
          title: 'Tips',
          content: app.globalData.i18n["control.tips.sendingFailed"],
          showCancel: false,
          confirmText: "OK"
        })
      }
    },
    //确认
    confirm: function () {
        var that = this

        if (this.data.password.length == 6) {
            var str = this.data.password
            
            var buf0 = new Uint8Array(str.length + 3);
            buf0[0] = 0xD1
            var tem = buf0[0];
            for (var i = 0; i < str.length; i++) {
                buf0[i + 1] = str.charCodeAt(i);
                tem += buf0[i + 1];
            }

            buf0[str.length + 1] = tem >> 8
            buf0[str.length + 2] = tem

            user.writeData(buf0)

            that.data.isLogin = true;

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isLogin && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {

                        user.writeData(buf0)
                    }
                    else {
                        that.data.isLogin = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        wx.showToast({
                          title: app.globalData.i18n["control.tips.loginerror"],
                          image: '/images/error.png',
                            duration: 1000
                        })
                      wx.hideTabBar({});
                      that.setData({
                        hiddenmodal: false,
                        inputShowed: true
                      })
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)
            wx.showTabBar({});
            that.setData({
              hiddenmodal: true,
              inputShowed: false
            });
            wx.showLoading({
              title: app.globalData.i18n["control.tips.connecting"],
                mask: true
            })
        }
        else {
            
          if (wx.getStorageSync("language") == "zh_CN") {
            wx.showToast({
              title: app.globalData.i18n["control.tips.password"],
              image: '/images/error.png',
              duration: 1000
            })
          } else {
            wx.showModal({
              title: 'Tips',
              content: app.globalData.i18n["control.tips.password"],
              showCancel: false,
              confirmText: "OK"
            })
          }
          wx.hideTabBar({});
          that.setData({
            hiddenmodal: false,
            inputShowed: true
          });
        }
    },

    touchstartlast: function () {
        if (this.data.isON)
        {
            this.setData({
                lasturl: "/images/lastsong-touched.png"
            })
        }

    },

    touchendlast: function () {
        var that = this
        if(this.data.isON)
        {
            var buffer = [0xc2, 0x02, 0x00, 0xc4];
            user.writeData(buffer)

            that.data.isLast = true;

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isLast && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {

                        user.writeData(buffer)
                    }
                    else {
                        that.data.isLast = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        that.showSendingFailed();
                      
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
                title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }

    },

    touchstartnext: function () {
        if (this.data.isON)
        {
            this.setData({
                nexturl: "/images/nextsong-touched.png"
            })
        }

    },

    touchendnext: function () {
        var that = this
        if (that.data.isON)
        {
            var buffer = [0xc2, 0x03, 0x00, 0xc5];
            user.writeData(buffer)

            that.data.isNext = true;

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isNext && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {

                        user.writeData(buffer)
                    }
                    else {
                        that.data.isNext = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        that.showSendingFailed();
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }
    },

    changeplay: function () {
        if (this.data.isON)
        {
            var that = this

            if (that.data.isPlay) {
                that.data.isStopPlay  = true;

                var buffer = [0xc2, 0x00, 0x00, 0xc2];
                user.writeData(buffer)

                time = 0;
                var interval = setInterval(function () {
                  if (that.data.isStopPlay && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                        time++;
                        if (time < 3) {
                            user.writeData(buffer)
                        }
                        else {
                            that.data.isStopPlay = false;
                            clearInterval(interval);
                            wx.hideLoading();
                            that.showSendingFailed();
                        }
                    }
                    else {
                        clearInterval(interval);
                    }

                }, 5000)

                wx.showLoading({
                  title: app.globalData.i18n["control.tips.sending"],
                    mask: true
                })
            } else {

                this.setData({
                    isStartPlay: true,
                })

                var buffer = [0xc2, 0x01, 0x00, 0xc3];
                user.writeData(buffer)

                time = 0;
                var interval = setInterval(function () {
                  if (that.data.isStartPlay && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                        time++;
                        if (time < 3) {
                            user.writeData(buffer)
                        }
                        else {
                            clearInterval(interval);
                            wx.hideLoading();
                            that.showSendingFailed();
                        }
                    }
                    else {
                        clearInterval(interval);
                    }

                }, 5000)

                wx.showLoading({
                  title: app.globalData.i18n["control.tips.sending"],
                    mask: true
                })
            }
        }
    },

    setmute: function(){
        var that = this
        if (that.data.isON)
        {
            var buffer = new Uint8Array(4);

            buffer[0] = 0xC4;

            if(that.data.isMute){
                buffer[1] = that.data.volume;
            }
            else{
                buffer[1] = 0;
            }

            buffer[2] = 0x00;
            buffer[3] = buffer[0] + buffer[1];
            user.writeData(buffer)

            that.data.isSetmute = true;

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isSetmute && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {

                        user.writeData(buffer)
                    }
                    else {
                        that.data.isSetmute = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        that.showSendingFailed();
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }

    },

    volchange:function(e){
        var that = this
        if (that.data.isON)
        {
            that.setData({
                volume: e.detail.value
            })
            
            var buffer = new Uint8Array(4);

            buffer[0] = 0xC4;
            buffer[1] = that.data.volume;
            buffer[2] = 0x00;
            buffer[3] = buffer[0] + buffer[1];
            user.writeData(buffer)

            that.data.isSetvolume = true

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isSetvolume && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {

                        user.writeData(buffer)
                    }
                    else {
                        that.data.isSetvolume = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        that.showSendingFailed();
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }

    },

    triggerpage: function () {
        if (this.data.isON)
        {
            var that = this
            that.data.isTrigger = true;

            var buffer = [0xc7, 0x00, 0xC7];
            user.writeData(buffer)

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isTrigger && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {
                        user.writeData(buffer)
                    }
                    else {
                        that.data.isTrigger = false;
                        clearInterval(interval);
                        wx.hideLoading();
                      that.showSendingFailed();
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })

            
        }

    },

    playpage: function () {
        if (this.data.isON)
        {
            var that = this
            that.data.isPlaymode = true;

            var buffer = [0xc9, 0x00, 0xC9];
            user.writeData(buffer)

            time = 0;
            var interval = setInterval(function () {
              if (that.data.isPlaymode && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                    time++;
                    if (time < 3) {
                        user.writeData(buffer)
                    }
                    else {
                        that.data.isPlaymode = false;
                        clearInterval(interval);
                        wx.hideLoading();
                        that.showSendingFailed();
                    }
                }
                else {
                    clearInterval(interval);
                }

            }, 5000)

            wx.showLoading({
              title: app.globalData.i18n["control.tips.sending"],
                mask: true
            })
        }

    },

    changeiamge: function () {                      
        if (this.data.isON)
        {
            var that = this
            if (that.data.isShow) {
                // this.setData({
                //     isStopRecord: true,
                // })
                that.data.isStopRecord = true
                console.log("1 isStopRecord = true");
                // var buffer = [0x7E, 0x00, 0x02, 0xA7, 0x02, 0x00, 0xA9, 0x7a];
                // var buf = new Uint8Array(buffer).buffer;
                // that.writeData(devId, buf)
                setTimeout(() => {
                  that.runStopRecord()
                }, 2000);

                wx.showLoading({
                  title: app.globalData.i18n["control.tips.sending"],
                    mask: true
                })
            } 
            else 
            {
                // this.setData({
                //     isStartRecord: true,
                // })
                that.data.isStartRecord = true

                // var buffer = [0x7E, 0x00, 0x02, 0xA7, 0x01, 0x00, 0xA8, 0x7a];
                // var buf = new Uint8Array(buffer).buffer;
                // this.writeData(devId, buf)

                var buffer = [0xA7, 0x01, 0x00, 0xA8];
                user.writeData(buffer)

                time = 0;
                var interval = setInterval(function () {
                  if (that.data.isStartRecord && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                        time++;

                        if (time < 3) {
                            user.writeData(buffer)

                        }
                        else {
                            that.data.isStartRecord = false
                            clearInterval(interval);
                            wx.hideLoading();
                            that.showSendingFailed();
                        }
                    }
                    else {
                        clearInterval(interval);
                    }

                }, 6000)

                wx.showLoading({
                  title: app.globalData.i18n["control.tips.sending"],
                    mask: true
                })
            }
        }

    },
    runStopRecord(){
      const that = this;
      var buffer = [0xA7, 0x02, 0x00, 0xA9];
      user.writeData(buffer)

      time = 0;
      var interval = setInterval(function () {
        if (that.data.isStopRecord && getCurrentPages().reverse()[0].route == that.data.currentPage) {
              time++;
              if (time < 3) {
                  user.writeData(buffer)
              }
              else {
                  that.data.isStopRecord = false
                  clearInterval(interval);
                  wx.hideLoading();
                that.showSendingFailed();
              }
          }
          else {
              clearInterval(interval);
          }

      }, 5000)
    },
    voicemanage:function(){

        wx.navigateTo({
            url: '../voicemanage/voicemanage'
        })
    },

    voicechange: function () {
        var that = this;
        wx.navigateTo({
            // url: '../voicechange/voicechange'
            url: '../localaudio/localaudio?state=' + (that.data.isShow?1:0),
            events: {
                // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
                onDeviceRecord: function(data) {
                  that.changeiamge();
                },
                onPageUnload:function(e){                  
                  eventChannel.off("onDeviceRecord");
                  // eventChannel.off("onPageUnload");
                  eventChannel = null;
                },
              },
            success: function(res) {
              // 通过eventChannel向被打开页面传送数据
              eventChannel = res.eventChannel;              
            }
        })
    },

    customize: function () {

        wx.navigateTo({
            url: '../customize/customize'
        })
    },

    storedevice:function(){
        var that = this
        
        

        var uuid = []
        if (wx.getStorageSync('devId')) {
            uuid = JSON.parse(wx.getStorageSync('devId'));
        }

        var isNew = true
        if (uuid.length > 0) {
            var i = 0;
            for (i = 0; i < uuid.length; i++) {
                if (uuid[i].uuid == app.globalData.devId) {
                    uuid[i].password = that.data.password;
                    isNew = false
                    break;
                }
            }
        }

        if(isNew){
            that.setData({
                award_one: {
                    uuid: app.globalData.devId,
                    password: that.data.password,
                }
            })
            var newarr = [that.data.award_one]; //对象转为数组

            uuid = uuid.concat(newarr);
        }

        //wx.setStorageSync("award_result", JSON.stringify(app.globalData.uuidpassword.concat(newarr)))
        wx.setStorage({
            key: 'devId',
            data: JSON.stringify(uuid),
            success: function (res) {
                // wx.showToast({
                //     title: 'storage success',
                //     duration: 1000
                // })
            },
            fail: function (res) { },
            complete: function (res) { },
        })
    },

    reconnectBle:function(devId){
      var that = this;
      user.reconnectBle(app.globalData.devId);

      wx.showLoading({
        title: app.globalData.i18n["control.tips.loading"],
        mask: true
      })
      this.data.initTimeoutCode = setTimeout(function () {
        wx.hideLoading();
        wx.stopBluetoothDevicesDiscovery({
          success: function (res) {

          },
        });

        wx.closeBLEConnection({
          deviceId: app.globalData.devId,
          complete: function (res) {
            if (getCurrentPages().reverse()[0].route != "pages/index/index"){
              wx.showModal({
                title: app.globalData.i18n["Tips"],
                content: app.globalData.i18n["control.tips.initialfailed"],
                showCancel: true,
                cancelText: app.globalData.i18n["cancel"],
                confirmText: app.globalData.i18n["control.tips.retry"],
                success: function (res) {
                  if (res.confirm) {
                    if (getCurrentPages().reverse()[0].route != "pages/index/index"){
                      that.reconnectBle(app.globalData.devId);
                    }
                  } else if (res.cancel) {
                    clearTimeout(that.data.initTimeoutCode);
                    wx.stopBluetoothDevicesDiscovery({

                    })
                    wx.closeBluetoothAdapter({
                      complete: function (res) {
                        wx.hideLoading();
                        if (getCurrentPages().reverse()[0].route != "pages/index/index") {
                          wx.reLaunch({
                            url: '../index/index',
                          });
                        }
                      },
                    });
                    
                  }
                }
              });
          }

          }});

      }, 5000);
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      var that = this;      
        wx.onAppShow((result) => {
          console.log(result);
          var isCurrentStartRecord = that.data.isStartRecord;
          if(isCurrentStartRecord){
            // 不显示提示,每次都出此提示,感觉不好.
            // wx.showLoading({
            //   title: app.globalData.i18n["control.tips.sending"],
            //     mask: true
            // })
          }
          // if(result.scene == 1001 || result.scene == 1011 ){
            //app是从主页进来的,还是扫描二维码进来的.
            var onIndex = getCurrentPages().findIndex(function(value,index){ 
              return value.route == "pages/control/control"
            });

            if(onIndex >= 0){
              this.queryRecordingState(isCurrentStartRecord);
            }
            
          // }
        });
          
        // 代理小程序的 setData，在更新数据后，翻译传参类型的字符串
        // util.resetSetData.call(this, langData);
        this.setData({
          i18n: app.globalData.i18n,
          currentPage: getCurrentPages().reverse()[0].route,
        });
        wx.setTabBarItem({
          index: 1,
          text: app.globalData.i18n["navBar.Timing"],
        });
        wx.setTabBarItem({
          index: 2,
          text: app.globalData.i18n["navBar.Me"],
        });
        wx.setTabBarItem({
          index: 0,
          text: app.globalData.i18n["navBar.Control"],
        });
        this.setData({
          inductionnum: app.globalData.i18n["control.inductionnum"],
          songnum: app.globalData.i18n["control.songnum"],
          trigger: app.globalData.i18n["control.trigger"],
          playmode: app.globalData.i18n["control.playmode"],
          record: app.globalData.i18n["control.record"],
          voice: app.globalData.i18n["control.voice"],
          doc: app.globalData.i18n["control.doc"],
          customize: app.globalData.i18n["control.customize"],
          recording: app.globalData.i18n["control.recording"],
        })

        this.reconnectBle(app.globalData.devId);

        user.userBleConnectFailed = res =>{
          console.log("连接失败");
          // wx.hideLoading()
          // clearTimeout(that.data.initTimeoutCode);
          // this.reconnectBle(app.globalData.devId);
        }
        wx.onBLEConnectionStateChange(function(res){
          if (res.deviceId == app.globalData.devId && res.connected == false && getCurrentPages().reverse()[0].route != "pages/index/index"){   
            wx.hideLoading()         
            wx.showToast({
              title: app.globalData.i18n["control.tips.disconnected"],
              image: '/images/error.png',
              complete: function (res) {
                wx.stopBluetoothDevicesDiscovery({

                })
                clearTimeout(that.data.initTimeoutCode);
                wx.closeBluetoothAdapter({
                  complete: function (res) {
                    wx.hideLoading();
                    wx.reLaunch({
                      url: '../index/index',
                    });
                  },
                });
              },
              duration:2000,
            })
            //连接断开;
          }
        })
//=======================================================
//connected        
        user.userBleConnected = res => {
            console.log("连接成功,未校验密码.")
            clearTimeout(that.data.initTimeoutCode);           

            // wx.hideTabBar({
                
            // })

            var uuid = []
            if (wx.getStorageSync('devId')) {
                uuid = JSON.parse(wx.getStorageSync('devId'));
            }
            
            if(uuid.length > 0){
                var i=0;
                for(i=0;i<uuid.length;i++){
                    if (uuid[i].uuid == app.globalData.devId){
                        that.data.password = uuid[i].password
                        break;
                    }
                }

              if (that.data.password.length > 0){                  
                    that.confirm();
                }else{
                wx.hideLoading()
                wx.hideTabBar({});
                  that.setData({
                    hiddenmodal: false,
                    inputShowed: true
                  })
                }
            }else{
              wx.hideLoading()
              wx.hideTabBar({});
              that.setData({
                hiddenmodal: false,
                inputShowed: true
              })
            }
            

        }
//=======================================================
//recieve data
        user.userGetdataCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据', user.hexstringFromBuffer(buf));
            if(bufView[3] == 0XDC){
              this.isQueryingRecordingState = false;
              if(eventChannel != null){
                eventChannel.emit('deviceRecordCallBack', {"state":bufView[4] == 0?0:1});
              }
              that.setData({
                isShow: bufView[4] == 0?false:true,
                
                // isStartRecord:bufView[4] == 0?false:true,
                // isStopRecord:bufView[4] == 0?true:false,
              })
              console.log("2 isStopRecord = "+that.data.isStopRecord);
            }
            if ((bufView[3] == 0xB2)) {
                that.data.isTime = false;

                var buf = new Uint8Array(8);
                buf[0] = bufView[4];
                buf[1] = bufView[5];
                buf[2] = bufView[6];
                buf[3] = bufView[7];
                buf[4] = bufView[8];
                buf[5] = bufView[9];
                buf[6] = bufView[10];
                buf[7] = bufView[11];

                var str = String.fromCharCode.apply(null, new Uint8Array(buf));

                console.log(str)
                //修复问题81;由于b2协议用于查询和播放导致的问题;
                //这种做法不能根本解决问题,只要播放时间足够短,并且设备语音足够多,在播放未结束,进入设备语音列表,马上返回,就很容易出现.
                if(that.data.isStartPlay || that.data.isNext || that.data.isLast || that.data.songnum == app.globalData.i18n["control.songnum"]){
                  that.setData({
                    songnum: str
                  })
                }
                

                if (that.data.isNext){
                    that.data.isNext = false;
                    that.setData({
                        nexturl: "/images/nextsong.png"
                    })
                    wx.hideLoading();
                }

                if (that.data.isLast) {
                    that.data.isLast = false;
                    that.setData({
                        lasturl: "/images/lastsong.png"
                    })
                    wx.hideLoading();
                }

                if (that.data.isStartPlay) {

                    that.setData({
                        isStartPlay: false
                    })

                    that.setData({
                        isPlay: true
                    })

                    wx.hideLoading()
                }

                if (that.data.isStopPlay) {

                    that.setData({
                        isStopPlay: false
                    })

                    that.setData({
                        isPlay: false
                    })

                    wx.hideLoading()
                }
                
                // wx.hideLoading()//查询歌曲名称,播放,上,下一曲时会出现,首次连接也会
            }

            if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB0) && (bufView[7] == 0x7A)) {
                if (bufView[4] == 0x01){
                    that.setData({
                        isPlay: true
                    })
                } else if (bufView[4] == 0x00) {
                    that.setData({
                        isPlay: false
                    })
                }
            }

            if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[7] == 0x7A)) {
                
                if (that.data.isSetmute){
                    that.data.isSetmute = false
                    if (that.data.isMute) {
                        that.setData({
                            isMute: false,
                            volumevalue: that.data.volume
                        })
                    }
                    else {
                        that.setData({
                            isMute: true,
                            volumevalue: 0
                        })
                    }
                }

                if (that.data.isSetvolume){
                    that.data.isSetvolume = false
                    if (that.data.isMute) {
                        that.setData({
                            isMute: false,
                            volumevalue: that.data.volume
                        })
                    }
                    else{
                        that.setData({
                            volumevalue: that.data.volume
                        })
                    }
                }
              if (bufView[4] != 0xa1 && bufView[4] != 0xa2 && bufView[4] != 0xa5 && bufView[4] != 0xdd){
                  wx.hideLoading();//设置定时,暂时不消失,b5才消失就好.
                }
                
            }
            if((bufView[0] == 0x7E) && (bufView[3] == 0xDD) && (bufView[bufView.length - 1] == 0x7A)){
              var config = 0;
              for(var index = 0; index < bufView.length - 7;index ++){
                config = config + (bufView[4+index] << (8 * (bufView.length - 7 - index - 1)));
              }
              var detail = {
                timingSettingEnable:         (bufView[4]&0b00000001) >> 0,//定时列表
                broadcastEnglishEnable:      (bufView[4]&0b00000010) >> 1,//英文播报
                relaySettingEnable:          (bufView[4]&0b00000100) >> 2,//继电器功能
                playWhenPowerOnSettingEnable:(bufView[4]&0b00001000) >> 3,//上电响
                proximitySensorEnable:       (bufView[4]&0b00010000) >> 4,// 近距离感应
                microwaveSensorEnable:       (bufView[4]&0b00100000) >> 5,// 微波感应
                magnetSensorEnable:          (bufView[4]&0b01000000) >> 6,// 磁性感应
                infraredSensorEnable:        (bufView[4]&0b10000000) >> 7,// 红外人体感应;
                outterSensorEnable:          (bufView[5]&0b10000000) >> 7,// 外部感应;
              }
              app.globalData.config = {"devId":app.globalData.devId,"value":config,"detail":detail};
              // app.globalData.config.push({"devId":app.globalData.devId,"value":config,"detail":detail});
              var saveConfig = wx.getStorageSync("config");
              if(saveConfig == ""){
                  saveConfig = [];
              }
              //其实没有缓存,才会去查询的,所以必然是没有的.到这里的existIndex必然是-1;
              var existIndex = saveConfig.findIndex(function(obj,index){
                  return obj.devId == app.globalData.devId;
              });
              if(existIndex == -1){
                saveConfig.push(app.globalData.config);                
              }else{
                saveConfig[existIndex] = app.globalData.config;
              }
              wx.setStorageSync('config', saveConfig);
              

              if(detail.broadcastEnglishEnable == 1){
                if(wx.getStorageSync("language") == "zh_CN"){
                  user.firstSetBroadcastLanguage(0);
                }else{
                  user.firstSetBroadcastLanguage(1);
                }
              }
              
            }

            if ((bufView[0] == 0x7E) && (bufView[3] == 0xC8) && (bufView[bufView.length - 1] == 0x7A)) {
              console.log("播报语言设置成功"+bufView[4])
              //用户设置过了语言,不在连接时,自动设置语言;
              wx.setStorageSync("userSetLanguage" + app.globalData.devId, bufView[4]);
            }

            if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB8) && (bufView[7] == 0x7A)) {
              //日期校正成功.
                that.data.isTime = false;               

                that.data.volume = bufView[4]

                that.setData({
                    volumevalue: bufView[4]
                })
                user.queryConfig({
                  success:function(res){
                    console.log("查询配置成功"+res.value);     
                    wx.hideLoading();               
                  },
                  fail:function(res){
                    console.log("查询配置失败");
                    wx.hideLoading()
                  },
                })
            }

            if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB2) && (bufView[7] == 0x7A)) {
                that.data.isTime = false;

                // wx.hideLoading(),这个是什么协议?,查询歌曲名称,播放时会引起,首次进来也会.

                that.setData({
                    volumevalue: bufView[4]
                })
            }

            if (that.data.isLogin) {
               console.log("校验密码中")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xBE) && (bufView[7] == 0x7A)) {
                    that.data.isLogin = false

                    if (bufView[4] == 1) {

                        //wx.hideLoading()

                        that.storedevice();

                        that.timeSet();
                        console.log("校验密码通过")
                        wx.showTabBar({});
                        that.setData({
                            hiddenmodal: true,
                            inputShowed:false
                        })

                        wx.showTabBar({
                            
                        })
                    } else if (bufView[4] == 0){
                      wx.hideLoading()
                      if (wx.getStorageSync("language") == "zh_CN") {
                        wx.showToast({
                          title: app.globalData.i18n["control.tips.passworderror"],
                          image: '/images/error.png',
                          duration: 1000,
                          complete:function(res){
                            wx.hideTabBar({});
                            that.setData({
                              hiddenmodal: false,
                              inputShowed: true
                            })
                          }
                        })
                      } else {
                        wx.showModal({
                          title: 'Tips',
                          content: app.globalData.i18n["control.tips.passworderror"],
                          showCancel: false,
                          confirmText: "OK",
                          success(res) {
                            if (res.confirm) {
                              wx.hideTabBar({});
                              that.setData({
                                hiddenmodal: false,
                                inputShowed: true
                              })
                            }
                          },
                        })
                      }
                    }
                }
            }

            if (that.data.isTrigger) {
                console.log("isTrigger")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xBD) && (bufView[7] == 0x7A)) {
                    that.data.isTrigger = false

                    app.globalData.triggernum = bufView[4];
                    wx.hideLoading()

                    wx.navigateTo({
                        url: '../trigger/trigger'
                    })
                }
            }

            if (that.data.isPlaymode) {
                console.log("isPlaymode")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB7) && (bufView[7] == 0x7A)) {
                    that.data.isPlaymode = false

                    app.globalData.playnum = bufView[4];
                    wx.hideLoading()

                    wx.navigateTo({
                        url: '../play/play'
                    })
                }
            }

            if (that.data.isStartRecord) {
                console.log("isStartRecord")//7E 00 03 B6 A7 01 01 5E 7A                
                
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x03) && (bufView[3] == 0xB6) && (bufView[4] == 0xA7) && (bufView[5] == 0x01) && (bufView[8] == 0x7A)) {
                    // _this.setData({
                    //     isStartRecord: false
                    // })
                    //开始录音成功
                    if(eventChannel != null){
                      eventChannel.emit('deviceRecordCallBack', {"state":1});
                    }
                    that.data.isStartRecord = false

                    that.setData({
                        isShow: true
                    })

                    //console.log(isShow)
                    if(getCurrentPages().reverse()[0].route == that.data.currentPage)
                    {
                      wx.hideLoading()
                    }

                }

                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x03) && (bufView[3] == 0xB6) && (bufView[4] == 0xA7) && (bufView[5] == 0x00) && (bufView[8] == 0x7A)) {
                    // _this.setData({
                    //     isStartRecord: false
                    // })
                    //开始录音失败
                    that.data.isStartRecord = false;
                    that.data.isStopRecord = true;
                    console.log("3 isStopRecord = "+that.data.isStopRecord);
                    wx.hideLoading()
                    
                  if (wx.getStorageSync("language") == "zh_CN") {
                    wx.showLoading({
                      title: app.globalData.i18n["control.tips.recordfailed"],
                      duration: 1000,
                    })
                  } else {
                    wx.showModal({
                      title: 'Tips',
                      content: app.globalData.i18n["control.tips.recordfailed"],
                      showCancel: false,
                      confirmText: "OK"
                    })
                  }
                }
            }

            if (that.data.isStopRecord) {              
                console.log("isStopRecord")//7E 00 03 B6 A7 01 01 5E 7A
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x03) && (bufView[3] == 0xB6) && (bufView[4] == 0xA7) && (bufView[5] == 0x01) && (bufView[8] == 0x7A)) {
                    // that.setData({
                    //     isStopRecord: false
                    // })
                    if(eventChannel != null){
                      eventChannel.emit('deviceRecordCallBack', {"state":0});
                    }
                    that.data.isStopRecord = false
                    //停止录音成功
                    that.setData({
                        isShow: false
                    })

                    //console.log(isShow)

                    wx.hideLoading()

                }
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x03) && (bufView[3] == 0xB6) && (bufView[4] == 0xA7) && (bufView[5] == 0x00) && (bufView[8] == 0x7A)) {
                    // that.setData({
                    //     isStopRecord: false
                    // })
                    //停止录音失败
                    that.data.isStopRecord = false
                    wx.hideLoading()
                    
                  if (wx.getStorageSync("language") == "zh_CN") {
                    wx.showLoading({
                      title: app.globalData.i18n["control.tips.recordstopfailed"],
                      duration: 1000,
                    })
                  } else {
                    wx.showModal({
                      title: 'Tips',
                      content: app.globalData.i18n["control.tips.recordstopfailed"],
                      showCancel: false,
                      confirmText: "OK"
                    })
                  }
                }
            }

            if (that.data.isShow) {
                console.log("isShow")//7E 00 03 B6 A7 02 01 5F 7A
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x03) && (bufView[3] == 0xB6) && (bufView[4] == 0xA7) && (bufView[5] == 0x02) && (bufView[8] == 0x7A)) {
                    // that.setData({
                    //     isStopRecord: false
                    // })
                    if(eventChannel != null){
                      eventChannel.emit('deviceRecordCallBack', {"state":0});
                    }
                    that.data.isStopRecord = false

                    that.setData({
                        isShow: false
                    })

                    //console.log(isShow)

                    //wx.hideLoading()
                    wx.showToast({                     
                      title: app.globalData.i18n["control.tips.recordstoped"],
                        duration: 1000,
                    })

                }

            }        
            
        }
    },
    //=======================================================
    loginset: function () {
        var that = this

        var str = app.globalData.password

        var buf0 = new Uint8Array(str.length + 3);
        buf0[0] = 0xD1
        var tem = buf0[0];
        for (var i = 0; i < str.length; i++) {
            buf0[i + 1] = str.charCodeAt(i);
            tem += buf0[i + 1];
        }

        buf0[str.length + 1] = tem >> 8
        buf0[str.length + 2] = tem

        user.writeData(buf0)

        time = 0;
        var interval = setInterval(function () {
          if (app.globalData.isLogin && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                time++;
                if (time < 3) {

                    user.writeData(buf0)
                }
                else {
                    that.data.isLogin = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    wx.showToast({
                      title: app.globalData.i18n["control.tips.loginerror"],
                      image: '/images/error.png',
                        duration: 1000
                    })          
                  wx.hideTabBar({});        
                  that.setData({
                    hiddenmodal: false,
                    inputShowed: true
                  })
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)
    },
//=======================================================
    timeSet:function(){
        var that = this

        that.data.isTime = true;

        var date = new Date()

        var year = date.getFullYear()
        var month = date.getMonth() + 1
        var day = date.getDate()
        var hour = date.getHours()
        var minute = date.getMinutes()
        var second = date.getSeconds()
        var week = date.getDay();

        var num = 11;
        var buf0 = new Uint8Array(num);
        buf0[0] = 0xA5
        buf0[1] = year >> 8;
        buf0[2] = year
        buf0[3] = month
        buf0[4] = week
        buf0[5] = day
        buf0[6] = hour
        buf0[7] = minute
        buf0[8] = second

        var tem = buf0[0];
        for (var i = 1; i < (num - 2); i++) {
            tem += buf0[i];
        }

        buf0[num - 2] = tem >> 8
        buf0[num - 1] = tem

        user.writeData(buf0)

        time = 0;
        var interval = setInterval(function () {
          if (that.data.isTime && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                time++;
                if (time < 3) {

                    user.writeData(buf0)
                }
                else {
                    that.data.isTime = false;
                    clearInterval(interval);
                    wx.hideLoading();
                    if (wx.getStorageSync("language") == "zh_CN") {
                      wx.showToast({
                        title: app.globalData.i18n["control.tips.adjustdateerror"],
                        image: '/images/error.png',
                        duration: 1000
                      })
                    } else {
                      wx.showModal({
                        title: 'Tips',
                        content: app.globalData.i18n["control.tips.adjustdateerror"],
                        showCancel: false,
                        confirmText: "OK"
                      })
                    }
                    
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.adjustingdate"],
            mask: true
        })
    },
    /**
     * 发送播报语言设置,第一次登录时,在校时成功后,发送语言设置,以后不再发送,用户如果需要修改,需要在"我的"那里修改.
     * 
     */
    soundLanguageSetting:function(fromAction){
      var that = this
      if((fromAction == "mine") 
      || (fromAction == 'first' && app.globalData.firstLanguageSetting == true)){

      }
      if(fromAction == "mine"){
        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })       
      }
    },
    /**
     * 查询小程序功能配置.这个函数没作用了.
     */
    queryConfigx:function(callback){
      user.queryConfig();
      time = 0;
      var interval = setInterval(function () {
        var config = app.globalData.config;
        config = config.filter(function(obj,index){
          return obj.devId == app.globalData.devId;
        });
        if (config.length < 0) {
              time++;
              if (time < 2) {
                user.queryConfig();//重试
              }else {
                  clearInterval(interval);//查询不到,放弃了.
                  callback.fail();
              }
          }
          else {
              clearInterval(interval);//查询到结果了
              wx.setStorageSync('config', app.globalData.config);
              callback.success(config[0]);
          }
        }, 
      500);
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
      console.log("onShow");
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {
      console.log("onHide");
    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.userGetdataCallback = null;
      wx.offAppShow();
      wx.offBLECharacteristicValueChange(null);
      wx.offBLEConnectionStateChange(null);
      wx.hideLoading({
        complete: (res) => {},
      })
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },
    queryRecordingState:function(isCurrentStartRecord){
      //查询当前录音状态.
      var that = this

      this.isQueryingRecordingState = true;

      var num = 3;
      var buf0 = new Uint8Array(num);
      buf0[0] = 0xDC;
      buf0[1] = 0x00;
      buf0[2] = 0XDC;
      user.writeData(buf0);

      var timex = 0;
      var interval = setInterval(function () {
        if (this.isQueryingRecordingState) {
          timex++;
              if (timex < 3) {
                  user.writeData(buf0)
              }
              else {
                  this.isQueryingRecordingState = false;
                  clearInterval(interval);
                  if(isCurrentStartRecord){
                    //避免影响其它的操作.需要加此判断.
                    wx.hideLoading();  
                  }                                 
              }
          }
          else {
              clearInterval(interval);
              if(isCurrentStartRecord){
                //避免影响其它的操作.
                wx.hideLoading();  
              }    
          }
      }, 3000);
    },
})