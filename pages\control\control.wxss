/* pages/control/control.wxss */
/* pages/control/control.wxss */
.maskWindow{
  height: 100%;
  width: 100%;
  position: fixed;
  background-color:rgba(0, 0, 0, .2);
  z-index: 2;
  top: 0;
  left: 0;
}

.imagesize{
 display:flex;       
 height: 100%;            
 justify-content: space-between; 
 align-items: center;        
}
.imagesize image { 
  width:400rpx;
  height:400rpx;
  }

.page {
  display: flex;
    align-content: flex-start;

}

.page1 {
    height:50px;
  display: flex;
  color: rgb(7, 5, 5);
  justify-content: space-between;
  align-items: center;
  font-size: 30;
}

.page-timr-1{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-timr1{
    display: flex;
    width: 100%;
    font-size: 15px;
    color: rgb(7, 5, 5);
    justify-content: center;
    margin-top: 20px;
}

.page-timr2{
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    color: rgb(7, 5, 5);
}

.page-timr3{
    display: flex;
    width: 100%;
    font-size: 15px;
    color: rgb(7, 5, 5);
    justify-content: center;
    align-items: center;
}

.page-timr4{

    font-size: 15px;
    margin-left: 5px;
}

.page-timr5{
    display: flex;
    width: 100%;
    font-size: 12px;
    color: rgb(7, 5, 5);
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.page-timr6{
    font-size: 15px;
    margin-right: 10px;
}

.page-timr7{
    display: flex;
    width: 100%;
    font-size: 12px;
    color: rgb(7, 5, 5);
    justify-content: center;
    align-items: center;
}

.bg-view{
    background: rgb(255, 255, 255);
}

.bg-gray{
    background: rgba(180, 180, 180, 0.4);
}

.bg-green{
    background: #00FF00;
}

.bg-red{
    background: #FF0000;
}

.bg-blue{
    background: #0000FF;
}

.btnImg {
  width: 20px;
  height: 20px;
  margin: 20px;
}

.btnImg1 {
  width: 60px;
  height: 60px;
  margin: 20px;
}

.btnImg2 {
  width: 20px;
  height: 20px;
  margin: 5px;
}

.btnImg3 {
  width: 30px;
  height: 30px;
  margin: 5px;
}

.sliderset {
  width: 200px;
}

.viewset{
    width: 100px;    
    height: 100px;
}

.viewset1{
    width: 98px;    
    height: 98px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 1px;
    
}

.viewset2{
    text-align: center;
    border: 1rpx solid rgba(7,17,27,0.1);
    line-height: 100rpx;
    height: 100rpx;
    margin: 20rpx;
}

.zan-dialog__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  /* 设置阴影半透明背景如： background: rgba(0, 0, 0, 0.4); */
  background: rgba(0, 0, 0, 0.4);
  display: none;
}

.zan-dialog__container {
  position: fixed;
  top: 200rpx;
  width: 650rpx;/*弹窗布局宽*/
  height: 350rpx;/*弹窗布局高，与下面弹出距离transform有关*/
  margin-left: 50rpx;
  margin-right: 50rpx;
  background: #f8f8f8;
  transform: translateY(500%);/*弹框弹出距离，与弹框布局高度有关，如300%表示弹起距离为3倍弹窗高度*/
  transition: all 0.4s ease;
  z-index: 12;
  border-radius: 20rpx;
  /*box-shadow: 0px 3px 3px 2px gainsboro;弹框的悬浮阴影效果，如不需要可注释该行*/
}

.zan-dialog--show .zan-dialog__container {
  transform: translateY(0);
}

.zan-dialog--show .zan-dialog__mask {
  display: block;
}

.modal-btn-wrapper{
 display: flex;
 flex-direction: row;
 height: 100rpx;
 line-height: 100rpx;
}
 
.cancel-btn, .confirm-btn{
 flex: 1;
 height: 100rpx;
 line-height: 100rpx;
 text-align: center;
 width: 324.5rpx;
 position:absolute; bottom:0;
 border-top: 2rpx solid rgba(7,17,27,0.1); 
 border-top-left-radius: 0;
 border-top-right-radius: 0;
 /* font-size: 32rpx; */
}
 
 .cancel-btn{
 /* border-right: 2rpx solid rgba(7,17,27,0.1); */
 border-bottom-left-radius: 20rpx;
 border-bottom-right-radius: 0;
}
 .confirm-btn{
 /* border-right: 2rpx solid rgba(7,17,27,0.1); */
 border-bottom-right-radius: 20rpx;
 border-bottom-left-radius: 0;
 color: green;
}
 .cancel-btn::after{
/* border: none; */
border-top: none;
border-right: none;
 border-top-left-radius: 0;
 border-top-right-radius: 0;
 border-bottom-right-radius: 0;
}
.confirm-btn::after{
  /* border: none; */
  border-top: none;
  border-left: none;
 /* border-right: 1rpx solid rgba(7, 17, 27, 0.1); */
 border-top-left-radius: 0;
 border-top-right-radius: 0;
 border-bottom-left-radius: 0;
}  