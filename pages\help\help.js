// pages/help/help.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        help1: "",
        help2: "蓝牙配对",
        help3: "语音更换",
        help4: "语音文件删除",
        help5: "语音文件制作指导",
        help6: "音频输出",
        help7: "音量调节",
        help8: "触发方式",
        help9: "供电方式",
        i18n: [],
    },

    weiset: function (e) {
        wx.navigateTo({
            url: '../helpwx/helpwx?doc='+e.currentTarget.dataset.doc,
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
      this.setData({
        i18n: getApp().globalData.i18n,
      });

    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})