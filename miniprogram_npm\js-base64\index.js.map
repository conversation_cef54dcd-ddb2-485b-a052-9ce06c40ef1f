{"version": 3, "sources": ["base64.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["/*\n *  base64.js\n *\n *  Licensed under the BSD 3-Clause License.\n *    http://opensource.org/licenses/BSD-3-Clause\n *\n *  References:\n *    http://en.wikipedia.org/wiki/Base64\n */\n;(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined'\n        ? module.exports = factory(global)\n        : typeof define === 'function' && define.amd\n        ? define(factory) : factory(global)\n}((\n    typeof self !== 'undefined' ? self\n        : typeof window !== 'undefined' ? window\n        : typeof global !== 'undefined' ? global\n: this\n), function(global) {\n    \n    // existing version for noConflict()\n    global = global || {};\n    var _Base64 = global.Base64;\n    var version = \"2.5.1\";\n    // if node.js and NOT React Native, we use Buffer\n    var buffer;\n    if (typeof module !== 'undefined' && module.exports) {\n        try {\n            buffer = eval(\"require('buffer').Buffer\");\n        } catch (err) {\n            buffer = undefined;\n        }\n    }\n    // constants\n    var b64chars\n        = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n    var b64tab = function(bin) {\n        var t = {};\n        for (var i = 0, l = bin.length; i < l; i++) t[bin.charAt(i)] = i;\n        return t;\n    }(b64chars);\n    var fromCharCode = String.fromCharCode;\n    // encoder stuff\n    var cb_utob = function(c) {\n        if (c.length < 2) {\n            var cc = c.charCodeAt(0);\n            return cc < 0x80 ? c\n                : cc < 0x800 ? (fromCharCode(0xc0 | (cc >>> 6))\n                                + fromCharCode(0x80 | (cc & 0x3f)))\n                : (fromCharCode(0xe0 | ((cc >>> 12) & 0x0f))\n                   + fromCharCode(0x80 | ((cc >>>  6) & 0x3f))\n                   + fromCharCode(0x80 | ( cc         & 0x3f)));\n        } else {\n            var cc = 0x10000\n                + (c.charCodeAt(0) - 0xD800) * 0x400\n                + (c.charCodeAt(1) - 0xDC00);\n            return (fromCharCode(0xf0 | ((cc >>> 18) & 0x07))\n                    + fromCharCode(0x80 | ((cc >>> 12) & 0x3f))\n                    + fromCharCode(0x80 | ((cc >>>  6) & 0x3f))\n                    + fromCharCode(0x80 | ( cc         & 0x3f)));\n        }\n    };\n    var re_utob = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFFF]|[^\\x00-\\x7F]/g;\n    var utob = function(u) {\n        return u.replace(re_utob, cb_utob);\n    };\n    var cb_encode = function(ccc) {\n        var padlen = [0, 2, 1][ccc.length % 3],\n        ord = ccc.charCodeAt(0) << 16\n            | ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8)\n            | ((ccc.length > 2 ? ccc.charCodeAt(2) : 0)),\n        chars = [\n            b64chars.charAt( ord >>> 18),\n            b64chars.charAt((ord >>> 12) & 63),\n            padlen >= 2 ? '=' : b64chars.charAt((ord >>> 6) & 63),\n            padlen >= 1 ? '=' : b64chars.charAt(ord & 63)\n        ];\n        return chars.join('');\n    };\n    var btoa = global.btoa ? function(b) {\n        return global.btoa(b);\n    } : function(b) {\n        return b.replace(/[\\s\\S]{1,3}/g, cb_encode);\n    };\n    var _encode = buffer ?\n        buffer.from && Uint8Array && buffer.from !== Uint8Array.from\n        ? function (u) {\n            return (u.constructor === buffer.constructor ? u : buffer.from(u))\n                .toString('base64')\n        }\n        :  function (u) {\n            return (u.constructor === buffer.constructor ? u : new  buffer(u))\n                .toString('base64')\n        }\n        : function (u) { return btoa(utob(u)) }\n    ;\n    var encode = function(u, urisafe) {\n        return !urisafe\n            ? _encode(String(u))\n            : _encode(String(u)).replace(/[+\\/]/g, function(m0) {\n                return m0 == '+' ? '-' : '_';\n            }).replace(/=/g, '');\n    };\n    var encodeURI = function(u) { return encode(u, true) };\n    // decoder stuff\n    var re_btou = new RegExp([\n        '[\\xC0-\\xDF][\\x80-\\xBF]',\n        '[\\xE0-\\xEF][\\x80-\\xBF]{2}',\n        '[\\xF0-\\xF7][\\x80-\\xBF]{3}'\n    ].join('|'), 'g');\n    var cb_btou = function(cccc) {\n        switch(cccc.length) {\n        case 4:\n            var cp = ((0x07 & cccc.charCodeAt(0)) << 18)\n                |    ((0x3f & cccc.charCodeAt(1)) << 12)\n                |    ((0x3f & cccc.charCodeAt(2)) <<  6)\n                |     (0x3f & cccc.charCodeAt(3)),\n            offset = cp - 0x10000;\n            return (fromCharCode((offset  >>> 10) + 0xD800)\n                    + fromCharCode((offset & 0x3FF) + 0xDC00));\n        case 3:\n            return fromCharCode(\n                ((0x0f & cccc.charCodeAt(0)) << 12)\n                    | ((0x3f & cccc.charCodeAt(1)) << 6)\n                    |  (0x3f & cccc.charCodeAt(2))\n            );\n        default:\n            return  fromCharCode(\n                ((0x1f & cccc.charCodeAt(0)) << 6)\n                    |  (0x3f & cccc.charCodeAt(1))\n            );\n        }\n    };\n    var btou = function(b) {\n        return b.replace(re_btou, cb_btou);\n    };\n    var cb_decode = function(cccc) {\n        var len = cccc.length,\n        padlen = len % 4,\n        n = (len > 0 ? b64tab[cccc.charAt(0)] << 18 : 0)\n            | (len > 1 ? b64tab[cccc.charAt(1)] << 12 : 0)\n            | (len > 2 ? b64tab[cccc.charAt(2)] <<  6 : 0)\n            | (len > 3 ? b64tab[cccc.charAt(3)]       : 0),\n        chars = [\n            fromCharCode( n >>> 16),\n            fromCharCode((n >>>  8) & 0xff),\n            fromCharCode( n         & 0xff)\n        ];\n        chars.length -= [0, 0, 2, 1][padlen];\n        return chars.join('');\n    };\n    var _atob = global.atob ? function(a) {\n        return global.atob(a);\n    } : function(a){\n        return a.replace(/\\S{1,4}/g, cb_decode);\n    };\n    var atob = function(a) {\n        return _atob(String(a).replace(/[^A-Za-z0-9\\+\\/]/g, ''));\n    };\n    var _decode = buffer ?\n        buffer.from && Uint8Array && buffer.from !== Uint8Array.from\n        ? function(a) {\n            return (a.constructor === buffer.constructor\n                    ? a : buffer.from(a, 'base64')).toString();\n        }\n        : function(a) {\n            return (a.constructor === buffer.constructor\n                    ? a : new buffer(a, 'base64')).toString();\n        }\n        : function(a) { return btou(_atob(a)) };\n    var decode = function(a){\n        return _decode(\n            String(a).replace(/[-_]/g, function(m0) { return m0 == '-' ? '+' : '/' })\n                .replace(/[^A-Za-z0-9\\+\\/]/g, '')\n        );\n    };\n    var noConflict = function() {\n        var Base64 = global.Base64;\n        global.Base64 = _Base64;\n        return Base64;\n    };\n    // export Base64\n    global.Base64 = {\n        VERSION: version,\n        atob: atob,\n        btoa: btoa,\n        fromBase64: decode,\n        toBase64: encode,\n        utob: utob,\n        encode: encode,\n        encodeURI: encodeURI,\n        btou: btou,\n        decode: decode,\n        noConflict: noConflict,\n        __buffer__: buffer\n    };\n    // if ES5 is available, make Base64.extendString() available\n    if (typeof Object.defineProperty === 'function') {\n        var noEnum = function(v){\n            return {value:v,enumerable:false,writable:true,configurable:true};\n        };\n        global.Base64.extendString = function () {\n            Object.defineProperty(\n                String.prototype, 'fromBase64', noEnum(function () {\n                    return decode(this)\n                }));\n            Object.defineProperty(\n                String.prototype, 'toBase64', noEnum(function (urisafe) {\n                    return encode(this, urisafe)\n                }));\n            Object.defineProperty(\n                String.prototype, 'toBase64URI', noEnum(function () {\n                    return encode(this, true)\n                }));\n        };\n    }\n    //\n    // export Base64 to the namespace\n    //\n    if (global['Meteor']) { // Meteor.js\n        Base64 = global.Base64;\n    }\n    // module.exports and AMD are mutually exclusive.\n    // module.exports has precedence.\n    if (typeof module !== 'undefined' && module.exports) {\n        module.exports.Base64 = global.Base64;\n    }\n    else if (typeof define === 'function' && define.amd) {\n        // AMD. Register as an anonymous module.\n        define([], function(){ return global.Base64 });\n    }\n    // that's it!\n    return {Base64: global.Base64}\n}));\n"]}