<view class="weui-search-bar {{searchState ? 'weui-search-bar_focusing' : ''}} {{extClass}}">
    <view class="weui-search-bar__form">
        <view class="weui-search-bar__box">
            <icon class="weui-icon-search" type="search" size="12"></icon>
            <input type="text" class="weui-search-bar__input" placeholder="{{placeholder}}" value="{{value}}" focus="{{focus}}" bindblur="inputBlur" bindfocus="inputFocus" bindinput="inputChange" />
            <text class="weui-icon-clear" wx:if="{{value.length > 0}}" bindtap="clearInput"></text>
        </view>
        <label class="weui-search-bar__label" bindtap="showInput">
            <icon class="weui-icon-search" type="search" size="12"></icon>
            <text class="weui-search-bar__text">搜索</text>
        </label>
    </view>
    <view wx:if="{{cancel && searchState}}" class="weui-search-bar__cancel-btn" bindtap="hideInput">{{cancelText}}</view>
</view>
<mp-cells ext-class=" {{'searchbar-result ' + extClass}}" wx:if="{{searchState && result.length > 0}}">
    <mp-cell class="result" bindtap="selectResult" body-class="weui-cell_primary" data-index="{{index}}" wx:for="{{result}}" wx:key="index" hover>
        <view>{{item.text}}</view>
    </mp-cell>
</mp-cells>