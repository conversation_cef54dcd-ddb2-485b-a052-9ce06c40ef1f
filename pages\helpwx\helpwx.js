// pages/helpwx/helpwx.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        helpwx1: "/images/helpwx1.png",
        helpwx2: "/images/helpwx2.png",
        helpwx3: "/images/helpwx3.png",
        docs:[{name:"wechat",src:""},
                {name:"iSound",src:"https://baike.baidu.com/item/iSound%E8%AF%AD%E9%9F%B3%E5%B9%BF%E5%91%8A%E6%9C%BA/4759464?fr=aladdin"},
                {name:"kingSound",src:"http://m.waytronic.com/m/ProductsStd_310.html"},
                {name:"WT-MS1",src:"https://mp.weixin.qq.com/s/Z84PGW5ZFYkIqFjIR00-Lg"}
            ],
        doc:{name:"wechat",src:""}
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.setData({
            doc:this.getDocByName(options.doc),
        });
    },

    getDocByName:function(docName){        
        var doc = this.data.docs.find(function(value,index){
            return value.name == docName;
        });
        if(doc == null){
            return "wechat";
        }
        return doc;
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})