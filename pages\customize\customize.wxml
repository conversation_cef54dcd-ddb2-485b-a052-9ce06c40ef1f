<!--pages/customize/customize.wxml-->
<view>
    <view class="textsize">
        <view class="viewset6">{{datetext}}</view>
        <input type='text' value="{{inputtext1}}" class="viewset2" bindinput="getInput1"></input>
        <view bindtap="send1">{{sendtext}}</view>
    </view>

    <view class="textsize" hidden="{{relaySettingEnable == 0}}">
        <view class="viewset6">{{timetext}}</view>
        <input type='text' value="{{inputtext2}}" class="viewset2" maxlength="2" bindinput="getInput2"></input>
        <view bindtap="send2">{{sendtext}}</view>
    </view>

    <view class="textsize">
        <view class="viewset6">{{key1}}</view>
        <input type='text' value="{{inputtext3}}" class="viewset2" maxlength="4" bindinput="getInput3"></input>
        <view bindtap="send3">{{sendtext}}</view>
    </view>

    <view class="textsize">
        <view class="viewset6">{{key2}}</view>
        <input type='text' value="{{inputtext4}}" class="viewset2" maxlength="4" bindinput="getInput4"></input>
        <view bindtap="send4">{{sendtext}}</view>
    </view>
    <view class="textsize">
        <view class="viewset6">{{i18n['customize.location']}}</view>
        <input type='text' value="{{location.longitude + ', ' + location.latitude}}" class="viewset2" hidden="{{!location.authorize}}" bindtap="chooseLocation" placeholder="{{customize.coordinate}}" style=""></input>
        <button bindtap="chooseLocation" hidden="{{!(!location.authorize && !location.reject)}}">{{i18n['customize.getlocation']}}</button>
        <button open-type="openSetting" hidden="{{!(!location.authorize && location.reject)}}">{{i18n['customize.LocationSetting']}}</button>
        <view bindtap="send4">{{sendtext}}</view>
    </view>
    
</view>