/* pages/selectindex/selectindex.wxss */
page{
  background-color: #ffffff;
}
.topdescribe{
  width: 90%;
  margin: auto;
  margin-top: 90px;
  display: flex;
  justify-content: start;
  align-items: center;
}
.titletxt{
  font-family: PingFangSC-medium;
  font-size: 40rpx;
  color: rgb(16,16,16);
}
.conectxt{
  font-family: PingFangSC-light;
  font-size: 32rpx;
}
.select{
  position: relative;
  top: 39px;
}
.voicemon{
  position: absolute;
  width: 92%;
  height: 132rpx;
  left: 50%;
  transform: translate(-50%);
  display: flex;
  align-items: center;
  border-radius: 8px;
  border: 1px solid rgba(187,187,187,1);
}
.forest{
  position: absolute;
  top: 166rpx;
  width: 92%;
  height: 132rpx;
  left: 50%;
  transform: translate(-50%);
  display: flex;
  align-items: center;
  border-radius: 8px;
  border: 1px solid rgba(187,187,187,1);
}
.conecttype{
  margin-left: 5px;
}
.contop{
  color: #101010;
  font-size: 32rpx;
  font-family: PingFangSC-medium;
}
.conbom{
  color: #0C6B3A;
  font-size: 28rpx;
  font-family: PingFangSC-light;
}