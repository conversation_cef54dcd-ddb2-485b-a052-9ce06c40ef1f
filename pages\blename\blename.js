// pages/blename/blename.js
const app = getApp()
var user = require('../../utils/ble.js');
var time = 0;
Page({

    /**
     * 页面的初始数据
     */
    data: {
        blename: "确定",
        bletext: "请输入新的蓝牙名字",
        inputname: "",
        isSetname: false,
        i18n:[],
    },

    getInput: function (e) {//方法1
        this.setData({
            inputname: e.detail.value
        });
    },

    setname: function () {
        var that = this;

        that.data.isSetname = true;
        var buffer = new Uint8Array(11);
        buffer[0] = 0xD3

        var str = that.data.inputname

        buffer[1] = str.charCodeAt(0)
        if (buffer[1] == 0x00) buffer[1] = 0x20
        buffer[2] = str.charCodeAt(1)
        if (buffer[2] == 0x00) buffer[2] = 0x20
        buffer[3] = str.charCodeAt(2)
        if (buffer[3] == 0x00) buffer[3] = 0x20
        buffer[4] = str.charCodeAt(3)
        if (buffer[4] == 0x00) buffer[4] = 0x20
        buffer[5] = str.charCodeAt(4)
        if (buffer[5] == 0x00) buffer[5] = 0x20
        buffer[6] = str.charCodeAt(5)
        if (buffer[6] == 0x00) buffer[6] = 0x20
        buffer[7] = str.charCodeAt(6)
        if (buffer[7] == 0x00) buffer[7] = 0x20
        buffer[8] = str.charCodeAt(7)
        if (buffer[8] == 0x00) buffer[8] = 0x20


        var tem = buffer[0] + buffer[1] + buffer[2] + buffer[3] + buffer[4] + buffer[5] + buffer[6] + buffer[7] + buffer[8]
        buffer[9] = tem >> 8
        buffer[10] = tem

        user.writeData(buffer)

        time = 0;
        var interval = setInterval(function () {
            if (that.data.isSetname) {
                time++;
                if (time < 3) {

                    user.writeData(buffer)
                }
                else {
                    that.data.isSetname = false;
                    clearInterval(interval);
                    wx.hideLoading();
                   
                  if (wx.getStorageSync("language") == "zh_CN") {
                    wx.showToast({
                      title: app.globalData.i18n["blename.tips.name"],
                      image: '/images/error.png',
                      duration: 1000
                    })
                  } else {
                    wx.showModal({
                      title: 'Tips',
                      content: app.globalData.i18n["blename.tips.name"],
                      showCancel: false,
                      confirmText: "OK"
                    })
                  }
                }
            }
            else {
                clearInterval(interval);
            }

        }, 1000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        var that = this
        this.setData({
          i18n: getApp().globalData.i18n,
        })
        user.blenameCallback = res => {
            var buf = new Uint8Array(res.value);
            var bufView = user.checkData(buf);

          console.log('收到新数据b', user.hexstringFromBuffer(buf))

            if (that.data.isSetname) {
                console.log("isSetname")
                if ((bufView[0] == 0x7E) && (bufView[1] == 0x00) && (bufView[2] == 0x02) && (bufView[3] == 0xB4) && (bufView[4] == 0xD3) && (bufView[7] == 0x7A)) {
                    that.data.isSetname = false

                    wx.hideLoading();

                    wx.showToast({
                        title: app.globalData.i18n["blename.tips.nameok"],
                        duration: 1000
                    })

                    const eventChannel = that.getOpenerEventChannel()
                    eventChannel.emit('acceptDataFromOpenedPage', {'blsname':that.data.inputname});
                }
            }

        }    
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
      user.blenameCallback = null;
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})