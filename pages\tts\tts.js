// pages/tts/tts.js
//reference：https://www.jianshu.com/p/68bd7e413156
//项目的根目录
//npm init
//npm install
//npm install js-base64
//npm install crypto-js
//工具>构建npm
// import CryptoJS from 'crypto-js'
// import { Base64 } from 'js-base64' //比较喜欢这种写法
// const CryptoJS = require('crypto-js');
var msg, token, IMEI, filePath, that;
const app = getApp();
import ttsUtil from "../../utils/ttsUtil.js";
// const ttsUtil = require('../../utils/ttsUtil.js');
var db, ttsList;
var audioPlayer = null;
const langData = require('../../langData.js');
/**'name':'发音人名称','engine':'xunfei','id':'发音人官方分配的编码' */
var ttsConfig = [
  {
    limit:'按照调用次数计费,预付费1.8元/千次,后付费3.5,100内算一次,后付费按天结算',
    maxLength:300,
    name: '阿里云',
    code: 'aliyun',
    appid: "请自行去阿里申请",
    key: "请自行去阿里申请",
    secret: "请自行去阿里申请",//"2lu6fdHjmEKzyvmYlxt27h0YIEuWqf",
    protocol: "https",
    url: "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/tts",
    tokenUrl: "https://nls-meta.cn-shanghai.aliyuncs.com/",
    person: [//https://help.aliyun.com/document_detail/84435.html?spm=a2c4g.11186623.6.593.c97b2bc0tL8xfl
      {'name': '温柔女声艾悦','engine': 'aliyun','id': 'Aiyue','lang': '普通话','langCode': 'zh_CN'},
      {'name': '严厉女声艾雅','engine': 'aliyun','id': 'Aiya','lang': '普通话','langCode': 'zh_CN'},
    {'name': '亲和女声艾夏','engine': 'aliyun','id': 'Aixia','lang': '普通话','langCode': 'zh_CN'},
    {'name': '甜美女声艾美','engine': 'aliyun','id': 'Aimei','lang': '普通话','langCode': 'zh_CN'},
    {'name': '自然女声艾雨','engine': 'aliyun','id': 'Aiyu','lang': '普通话','langCode': 'zh_CN'},
    {'name': '严厉女声艾婧','engine': 'aliyun','id': 'Aijing','lang': '普通话','langCode': 'zh_CN'},
      {'name': '粤语女声姗姗','engine': 'aliyun','id': 'Shanshan','lang': '普通话','langCode': 'zh_CN'},      
      {'name': '儿童音思彤','engine': 'aliyun','id': 'Sitong','lang': '普通话','langCode': 'zh_CN'},
      {'name': 'Olivia','engine': 'aliyun','id': 'Olivia','lang': '英文','langCode': 'en'},
      {'name': '浙普女声艾娜','engine': 'aliyun','id': 'Aina','lang': '普通话','langCode': 'zh_CN'},
      {'name': '标准女声小云','engine': 'aliyun','id': 'Xiaoyun','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准男声小刚','engine': 'aliyun','id': 'Xiaogang','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准女声小梦','engine': 'aliyun','id': 'Xiaomeng','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准男声小威','engine': 'aliyun','id': 'Xiaowei','lang': '普通话','langCode': 'zh_CN'},
    {'name': '温柔女声若兮','engine': 'aliyun','id': 'Ruoxi','lang': '普通话','langCode': 'zh_CN'},
    {'name': '温柔女声思琪','engine': 'aliyun','id': 'Siqi','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准女声思佳','engine': 'aliyun','id': 'Sijia','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准男声思诚','engine': 'aliyun','id': 'Sicheng','lang': '普通话','langCode': 'zh_CN'},
    {'name': '温柔女声艾琪','engine': 'aliyun','id': 'Aiqi','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准女声艾佳','engine': 'aliyun','id': 'Aijia','lang': '普通话','langCode': 'zh_CN'},
    {'name': 'Halen','engine': 'aliyun','id': 'Halen','lang': '英文','langCode': 'en'},
    {'name': 'Harry','engine': 'aliyun','id': 'Harry','lang': '英文','langCode': 'en'}, 
    {'name': 'Eric','engine': 'aliyun','id': 'Eric','lang': '英文','langCode': 'en'},      
    {'name': '萝莉女声小北','engine': 'aliyun','id': 'Xiaobei','lang': '普通话','langCode': 'zh_CN'},
    {'name': '儿童音艾彤','engine': 'aliyun','id': 'Aitong','lang': '普通话','langCode': 'zh_CN'},
    {'name': '萝莉女声艾薇','engine': 'aliyun','id': 'Aiwei','lang': '普通话','langCode': 'zh_CN'},
    {'name': '萝莉女声艾宝','engine': 'aliyun','id': 'Aibao','lang': '普通话','langCode': 'zh_CN'},
    {'name': 'Emily','engine': 'aliyun','id': 'Emily','lang': '英文','langCode': 'en'},
    {'name': 'Luna','engine': 'aliyun','id': 'Luna','lang': '英文','langCode': 'en'},
    {'name': 'Luca','engine': 'aliyun','id': 'Luca','lang': '英文','langCode': 'en'},
    {'name': 'Wendy','engine': 'aliyun','id': 'Wendy','lang': '英文','langCode': 'en'},
    {'name': 'William','engine': 'aliyun','id': 'William','lang': '英文','langCode': 'en'},    
    {'name': '标准男声艾诚','engine': 'aliyun','id': 'Aicheng','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准男声艾达','engine': 'aliyun','id': 'Aida','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准女声宁儿','engine': 'aliyun','id': 'Ninger','lang': '普通话','langCode': 'zh_CN'},
    {'name': '标准女声瑞琳','engine': 'aliyun','id': 'Ruilin','lang': '普通话','langCode': 'zh_CN'},
    {'name': '甜美女声阿美','engine': 'aliyun','id': 'Amei','lang': '普通话','langCode': 'zh_CN'},
    {'name': '温柔女声小雪','engine': 'aliyun','id': 'Xiaoxue','lang': '普通话','langCode': 'zh_CN'},
    {'name': '温柔女声思悦','engine': 'aliyun','id': 'Siyue','lang': '普通话','langCode': 'zh_CN'},
    {'name': '甜美女声小美','engine': 'aliyun','id': 'Xiaomei','lang': '普通话','langCode': 'zh_CN'},
    {'name': '浙普女声伊娜','engine': 'aliyun','id': 'Yina','lang': '普通话','langCode': 'zh_CN'},
    {'name': '严厉女声思婧','engine': 'aliyun','id': 'Sijing','lang': '普通话','langCode': 'zh_CN'},
  ],
    speed: {
      min: -500,
      max: 500,
      step: 5,
      value: 0,
    },
    /**语速 */
    volume: {
      min: 1,
      max: 100,
      step: 1,
      value: 50,
    },
    /**音量*/
    anchor: {
      min: -500,
      max: 500,
      step: 5,
      value: 0,     
    },
    /**语调 */
    music: '',
    /**背景音乐地址,部分引擎可以有背景音乐 */
    text: "",
  },
  {
    limit:'语音合成配额：每个小程序100次/分钟，2w次/天.',
    maxLength:333,//1000字节.
    name: '微信同声传译',
    code: 'WechatSI',
    appid: "wx069ba97219f66d99",
    key: "",
    secret: "",
    protocol: "",
    url: "",
    tokenUrl: "",
    person: [{
      'name': '标准中文',
      'engine': 'weixin',
      'id': 'zh_CN',
      'lang': '普通话',
      'langCode': 'zh_CN'
    }, {
        'name': 'Standard English',
      'engine': 'weixin',
      'id': 'en_US',
      'lang': '英文',
      'langCode': 'en_US'
    }, 
  ],
    speed: {
      min: 1,
      max: 100,
      step: 1,
      value: 50,
      hidden:true,
    },
    /**语速 */
    volume: {
      min: 1,
      max: 100,
      step: 1,
      value: 50,
      hidden:true,
    },
    /**音量*/
    anchor: {
      min: 0,
      max: 100,
      step: 1,
      value: 50,
      hidden:true,
    },
    /**语调 */
    music: '',
    /**背景音乐地址,部分引擎可以有背景音乐 */
    text: "",
  },
  {
    limit:'每年65w次免费访问,8000字节',//但是微信有文件大小限制,不可保存过大的文件10M.
    maxLength:1024,
    name: '科大讯飞',
    code: 'xunfei',
    appid: "请自行去科大讯飞申请",
    key: "请自行去科大讯飞申请",
    secret: "请自行去科大讯飞申请",
    protocol: 'wss',
    url: 'wss://tts-api.xfyun.cn/v2/tts',
    tokenUrl: '',
    person: [{
        'name': '讯飞小燕',
        'engine': 'xunfei',
        'id': 'xiaoyan',
        'lang': '普通话',
        'langCode': 'intp65'
      },
      {
        'name': '讯飞许久',
        'engine': 'xunfei',
        'id': 'aisjiuxu',
        'lang': '普通话',
        'langCode': 'intp65'
      },
      {
        'name': '讯飞小萍',
        'engine': 'xunfei',
        'id': 'aisxping',
        'lang': '普通话',
        'langCode': 'intp65'
      },
      {
        'name': '讯飞小婧',
        'engine': 'xunfei',
        'id': 'aisjinger',
        'lang': '普通话',
        'langCode': 'intp65'
      },
      {
        'name': '讯飞许小宝',
        'engine': 'xunfei',
        'id': 'aisbabyxu',
        'lang': '普通话',
        'langCode': 'intp65'
      },
    ],
    speed: {
      min: 1,
      max: 100,
      step: 1,
      value: 50,
    },
    /**语速 */
    volume: {
      min: 1,
      max: 100,
      step: 1,
      value: 50,
    },
    /**音量*/
    anchor: {
      min: 0,
      max: 100,
      step: 1,
      value: 50,
    },
    /**语调 */
    music: '',
    /**背景音乐地址,部分引擎可以有背景音乐 */
    text: "",
  },
  {
    limit:'每秒同时访问限制5人',
    maxLength:700,
    name: '百度',
    code: 'baidu',
    appid: '请自行去百度申请',//
    key: '请自行去百度申请',//
    secret: '请自行去百度申请',//
    protocol: 'https',
    url: 'https://tsn.baidu.com/text2audio',
    tokenUrl: 'https://openapi.baidu.com/oauth/2.0/token',
    person: [{
        'name': '度小宇',
        'engine': 'baidu',
        'id': '1',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度小美',
        'engine': 'baidu',
        'id': '0',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度逍遥',
        'engine': 'baidu',
        'id': '3',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度丫丫',
        'engine': 'baidu',
        'id': '4',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度博文',
        'engine': 'baidu',
        'id': '106',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度小童',
        'engine': 'baidu',
        'id': '110',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度小萌',
        'engine': 'baidu',
        'id': '111',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度米朵',
        'engine': 'baidu',
        'id': '103',
        'lang': '中英混合',
        'langCode': 'zh'
      },
      {
        'name': '度小娇',
        'engine': 'baidu',
        'id': '5',
        'lang': '中英混合',
        'langCode': 'zh'
      },
    ],
    speed: {
      min: 0,
      max: 15,
      step: 1,
      value: 5,
    },
    volume: {
      min: 0,
      max: 15,
      step: 1,
      value: 5,
    },
    anchor: {
      min: 0,
      max: 15,
      step: 1,
      value: 5,
    },
    music: '',
    text: "",
  },
  {
    limit:'1000次/日',
    maxLength:300,
    name: '思必驰',
    code: 'sibici',
    appid: '请自行去思必驰申请',
    key: '',
    secret: '请自行去思必驰申请',
    protocol: 'https',
    //url:'https://tts.dui.ai/runtime/v2/synthesize?',
    url: 'https://wxasr.waytronic.tech/index.php?r=tts/getzaudio&engineId=sibici',
    tokenUrl: '',
    person: [
      {
        'name': '甜美女神小玲',
        'engine': 'sibici',
        'id': 'zhilingfa',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },{
        'name': '女老师风吟',
        'engine': 'sibici',
        'id': 'feyinf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准女声小妖',
        'engine': 'sibici',
        'id': 'xiyaof',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '可爱男声连连',
        'engine': 'sibici',
        'id': 'lzliaf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准平和小佚',
        'engine': 'sibici',
        'id': 'anonyf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准女声小浩',
        'engine': 'sibici',
        'id': 'lucyfa',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },      
      {
        'name': '磁性男声俞老师',
        'engine': 'sibici',
        'id': 'yukaim_all',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '温柔女声小兰',
        'engine': 'sibici',
        'id': 'gqlanf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '商务女声璃璃',
        'engine': 'sibici',
        'id': 'lili1f_shangwu',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准女声瑶瑶',
        'engine': 'sibici',
        'id': 'luyaof',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品知性女声晶晶',
        'engine': 'sibici',
        'id': 'jjingfp',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '鬼故事绝音',
        'engine': 'sibici',
        'id': 'juyinf_guigushi',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '女学生初阳',
        'engine': 'sibici',
        'id': 'cyangf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '女老师小妖',
        'engine': 'sibici',
        'id': 'xiyaof_laoshi',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '可爱女童然然',
        'engine': 'sibici',
        'id': 'qianranfa',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准女声安宁',
        'engine': 'sibici',
        'id': 'aningf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '女声杨阿姨',
        'engine': 'sibici',
        'id': 'yaayif',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '沉稳幽默纲叔',
        'engine': 'sibici',
        'id': 'gdgm',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '飘逸女声小静',
        'engine': 'sibici',
        'id': 'xjingf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '清新女声小妖',
        'engine': 'sibici',
        'id': 'xiyaof_qingxin',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '甜美女神小玲',
        'engine': 'sibici',
        'id': 'zhilingf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '女老师行者',
        'engine': 'sibici',
        'id': 'xizhef',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '电台女声璃璃',
        'engine': 'sibici',
        'id': 'lili1f_diantai',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准男声小军',
        'engine': 'sibici',
        'id': 'xijunm',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '风趣幽默星哥',
        'engine': 'sibici',
        'id': 'zxcm',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准男声考拉',
        'engine': 'sibici',
        'id': 'kaolam',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准男声秋木',
        'engine': 'sibici',
        'id': 'qiumum',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准女声蓝雨',
        'engine': 'sibici',
        'id': 'lanyuf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品温柔女声小兰',
        'engine': 'sibici',
        'id': 'gqlanfp',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准男声小睿',
        'engine': 'sibici',
        'id': 'tzruim',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准男声小江',
        'engine': 'sibici',
        'id': 'wjianm',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '知性女声晶晶',
        'engine': 'sibici',
        'id': 'jjingf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品甜美女神小玲',
        'engine': 'sibici',
        'id': 'zhilingfp',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '故事女声小静',
        'engine': 'sibici',
        'id': 'xjingf_gushi',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '可爱男童连连',
        'engine': 'sibici',
        'id': 'lzliafa',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '可爱女童然然',
        'engine': 'sibici',
        'id': 'qianranf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '清新女声零八',
        'engine': 'sibici',
        'id': 'linbaf_qingxin',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '娱报女声璃璃',
        'engine': 'sibici',
        'id': 'lili1f_yubo',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准女童佚佚',
        'engine': 'sibici',
        'id': 'anonyg',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品客服女声芳芳',
        'engine': 'sibici',
        'id': 'gdfanfp',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品欢快女神小玲',
        'engine': 'sibici',
        'id': 'zhilingfp_huankuai',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '邻家女声小妮',
        'engine': 'sibici',
        'id': 'hyanif',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '高冷女声零八',
        'engine': 'sibici',
        'id': 'linbaf_gaoleng',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准女声朱株儿',
        'engine': 'sibici',
        'id': 'zzherf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '故事女声砖砖',
        'engine': 'sibici',
        'id': 'zzhuaf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '邻家女声小妮',
        'engine': 'sibici',
        'id': 'hyanifa',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品甜美女声小静',
        'engine': 'sibici',
        'id': 'xjingfp',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品可爱男童连连',
        'engine': 'sibici',
        'id': 'lzliafp',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品娱播女声麻豆',
        'engine': 'sibici',
        'id': 'madoufp_yubo',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '清纯女声考拉',
        'engine': 'sibici',
        'id': 'kaolaf',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '清亮女声小洁',
        'engine': 'sibici',
        'id': 'smjief',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '男声季老师',
        'engine': 'sibici',
        'id': 'jlshim',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准男童方方',
        'engine': 'sibici',
        'id': 'gdfanf_boy',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '标准男童堂堂',
        'engine': 'sibici',
        'id': 'boy',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品女童贝壳',
        'engine': 'sibici',
        'id': 'xbekef',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品男声小军',
        'engine': 'sibici',
        'id': 'xijunma',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '淡定风趣葛爷',
        'engine': 'sibici',
        'id': 'geyou',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品女学生初阳',
        'engine': 'sibici',
        'id': 'cyangfp',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '精品温柔女声麻豆',
        'engine': 'sibici',
        'id': 'madoufp_wenrou',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
      {
        'name': '清脆女生小颖',
        'engine': 'sibici',
        'id': 'lzyinfa',
        'lang': '中英混合',
        'langCode': 'zh-CN'
      },
    ],
    speed: {
      min: 0.7,
      max: 2,
      step: 0.1,
      value: 1,
    },
    volume: {
      min: 0,
      max: 100,
      step: 1,
      value: 80,
    },
    anchor: {
      min: 0,
      max: 100,
      step: 1,
      value: 50,
      hidden: true
    },
    music: '',
    text: "",
  },
];

Page({

  /**
   * 页面的初始数据
   */
  data: {
    i18n:[],
    ttsConfig: ttsConfig,
    engineIndex: 0,
    personIndex: 0,
    downloadurl:"",
    errMsg:"",
    dialog:{visible:'hidden', msg:""},
    shareOpen:false,//是否分享直接进入本界面的.
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    that = this;
    // wx.cloud.init();
    // db = wx.cloud.database();    
    // this.ttsSecretQuery();
    // console.log(Base64);
    // console.log(CryptoJS);
    // var signatureSha = CryptoJS.HmacSHA256("signatureOrigin", "apiSecret");
    ttsList = options.ttsList;
    // console.log("ttsList" + ttsList);
    if(app.globalData.i18n.length == 0){
      console.log("shareOpen:true");
      this.setData({
        shareOpen:true,
      })
      app.globalData.i18n = langData.data.i18n;
      var ttsDir = wx.env.USER_DATA_PATH + "/TTSList";
      const fileManager = wx.getFileSystemManager();
      try {
        
        fileManager.accessSync(ttsDir);
        console.log(ttsDir+"目录已经存在")
        fileManager.readdir({
          dirPath:ttsDir,
          success:function(res){            
            ttsList = res.files;
          },
          fail:function(res){
    
          }
        });
      } catch (error) {
        fileManager.mkdirSync(ttsDir,true);
      }
    }
    this.setData({
      ttsConfig:ttsConfig,
      i18n: app.globalData.i18n,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    if(audioPlayer != null){
      audioPlayer.destroy();
      audioPlayer = null;
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  },
  onBaiduTTSStart: function(e) {

  },
  ttsSecretQuery: function(e) {
    var storeTTSConfig = this.getStoreTTSConfig();
    if (storeTTSConfig != "" && storeTTSConfig != null) {
      ttsConfig = storeTTSConfig;
      this.setData({
        ttsConfig: ttsConfig,
      })
      return;
    }

    db.collection('engine').where({
      _openid: this.data.openid,
      enable: true,
    }).get({
      success: res => {
        var ttsSecret = res.data;
        this.setData({
          ttsSecret: ttsSecret
        })
        console.log('[数据库] [查询记录] 成功: ', res)
        ttsConfig = ttsConfig.filter(function(tts, configIndex) {

          var secretIndex = ttsSecret.findIndex(function(secret) {
            return secret.code == tts.code;
          });

          if (secretIndex >= 0) {
            tts.appid = ttsSecret[secretIndex].appid;
            tts.key = ttsSecret[secretIndex].key;
            tts.secret = ttsSecret[secretIndex].secret;
            return true;
          }
          return false;
        });
        wx.setStorageSync('ttsConfig' + app.globalData.build, ttsConfig);
        this.setData({
          ttsConfig: ttsConfig,
        })
      },
      fail: err => {
        // wx.showToast({
        //   icon: 'none',
        //   title: '查询记录失败'
        // })
        console.error('[数据库] [查询记录] 失败：', err)
      }
    });
  },
  getStoreTTSConfig: function(e) {
    return wx.getStorageSync('ttsConfig' + app.globalData.build);
  },
  platformPickerChanged: function(e) {
    console.log(e.detail.value)
    var engineIndex = that.data.engineIndex;
    ttsConfig[e.detail.value].text = ttsConfig[engineIndex].text;
    this.setData({
      engineIndex: e.detail.value,
      personIndex: 0,
      ttsConfig: ttsConfig,
    })
  },
  personPickerChanged: function(e) {
    console.log(e.detail.value)
    this.setData({
      personIndex: e.detail.value,
    })
  },
  getWebsocketUrl: function(engineCode) {
    switch (engineCode) {
      case "xunfei":
        {
          return new Promise((resolve, reject) => {
            var apiKey = ttsConfig[that.data.engineIndex].key;
            var apiSecret = ttsConfig[that.data.engineIndex].secret;
            var appid = ttsConfig[that.data.engineIndex].appid;
            var url = ttsConfig[that.data.engineIndex].url;
            var host = "tts-api.xfyun.cn"; //location.host;
            var date = new Date().toGMTString();
            var algorithm = 'hmac-sha256';
            var headers = 'host date request-line';
            var signatureOrigin = `host: ${host}\ndate: ${date}\nGET /v2/tts HTTP/1.1`;
            var signatureSha = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
            var signature = CryptoJS.enc.Base64.stringify(signatureSha);
            var authorizationOrigin = `api_key="${apiKey}", algorithm="${algorithm}", headers="${headers}", signature="${signature}"`;
            var authorization = Base64.btoa(authorizationOrigin);
            url = `${url}?authorization=${authorization}&date=${date}&host=${host}`
            resolve(url)
          });
        }
    }
    return null;
  },
  onTTSPlayButtonClicked: function(e) {
    that.setData({
      errMsg:"",
    })
    if(ttsConfig[that.data.engineIndex].text.trim() == ""){
      return;
    }
    wx.showLoading({
      title: that.data.i18n['control.tips.sending'],
    })
    var engineIndex = that.data.engineIndex;
    var personIndex = that.data.personIndex;
    var protocol = ttsConfig[engineIndex].protocol;
    if (protocol == "wss") {
      ttsUtil.configWebsocket({
        code: ttsConfig[engineIndex].code,
        success: function(res) {
          let pcmData = res.audio;
          if (!(res.code == "xunfei" && res.state == 2)) {
            return;
          }
          let wavData = ttsUtil.encodeWAV(new DataView(pcmData.buffer), 16000);
          const fileManager = wx.getFileSystemManager()
          var ttsAudioDir = wx.env.USER_DATA_PATH + "/TTSList";
          var name = ttsConfig[engineIndex].text + ".wav";
          var newName = ttsUtil.newNameOfDuplicatFile(ttsList, name)
          if (name != newName) {
            ttsList = ttsList + "," + newName;
          } else {
            ttsList = ttsList + "," + name;
          }
          var scr = ttsAudioDir + "/" + newName
          fileManager.writeFile({
            filePath: scr,
            data: wavData.buffer,
            encoding: "binary",
            success: function(res) {
              console.log(res);
              if(audioPlayer == null){
                audioPlayer = wx.createInnerAudioContext();
              }
              audioPlayer.src = scr;
              audioPlayer.play();
              if(!that.data.shareOpen){
                const eventChannel = that.getOpenerEventChannel()
                eventChannel.emit('acceptDataFromOpenedPage', {
                  'ttsNewFile': newName
                });
              }
              
              wx.hideLoading();
            },
            fail: function(res) {
              console.log(res);
              wx.hideLoading();
              // wx.showToast({
              //   title: res.errMsg,
              // })
              if(res.errMsg == "writeFile:fail the maximum size of the file storage limit is exceeded"){
                that.setData({
                  errMsg: that.data.i18n['localaudio.tip.maxsizelimted'],
                })
              }else{
                that.setData({
                  errMsg: res.errMsg,
                })
              }
            }
          });
        },
        fail: function(res) {
          wx.hideLoading();
          if(res.errMsg == "exception onOpen fail code:9, msg:Timer Expired"){
            that.setData({
              errMsg:that.data.i18n['localaudio.tip.neterr'],
            })
          }else{
            that.setData({
              errMsg:res.errMsg
            })
          }
          
        },
      });
    }else{
      ttsUtil.configHttp({
        code: ttsConfig[engineIndex].code,
        success:function(res){         
          if(res.url){
            //ae.weixin.qq.com需要添加到域名列表。
            var ttsAudioDir = wx.env.USER_DATA_PATH + "/TTSList";
            var ext = res.ext;
            var tempName = new Date().getTime();

            wx.downloadFile({

              url: res.url,
              filePath: ttsAudioDir + "/" + tempName + "." + ext,
              success:function(ress){
                var tempFilePath = ress.tempFilePath || ress.filePath;
                const fileManager = wx.getFileSystemManager()                
                var lastPoint = tempFilePath.lastIndexOf(".");
                var name = tempFilePath.substr(0, lastPoint);
                ext = ext || tempFilePath.substr(lastPoint + 1, 5);
                if (ext == "html"){
                  wx.hideLoading();
                  wx.showToast({
                    title: that.data.i18n['tts.err.tts'],
                  })                  
                  return;
                }
                that.setData({
                  downloadurl: res.url,
                })
                name = ttsConfig[engineIndex].text + "." + ext;
                var newName = ttsUtil.newNameOfDuplicatFile(ttsList, name)
                if (name != newName) {
                  ttsList = ttsList + "," + newName;
                } else {
                  ttsList = ttsList + "," + name;
                }
                var scr = ttsAudioDir + "/" + newName;
                
                fileManager.rename({
                  oldPath:tempFilePath,
                  newPath:scr,
                  success:function(resss){
                    if(audioPlayer == null){
                      audioPlayer = wx.createInnerAudioContext();
                    }
                    audioPlayer.src = scr;//res.url || res.src;
                    console.log("src=" + scr);
                    audioPlayer.play();
                    wx.hideLoading();
                    if(!that.data.shareOpen){
                      const eventChannel = that.getOpenerEventChannel()
                      eventChannel.emit('acceptDataFromOpenedPage', {
                        'ttsNewFile': newName
                      });                    
                    }
                  },
                  fail:function(resss){
                    wx.hideLoading();                    
                    if(resss.errMsg == "rename:fail ERR_EXCEED_DIRECTORY_MAX_SIZE"){
                      that.setData({
                        errMsg: that.data.i18n['localaudio.tip.maxsizelimted'],
                      })
                    }else{
                      that.setData({
                        errMsg: resss.errMsg,
                      })
                    }
                  }
                });                
              },
              fail:function(ress){
                wx.hideLoading();
                if(ress.errMsg == "downloadFile:fail fail the maximum size of the file storage limit is exceeded"){
                  that.setData({
                    errMsg: that.data.i18n['localaudio.tip.maxsizelimted'],
                  })
                }else{
                  that.setData({
                    errMsg: ress.errMsg,
                  })
                }

              },
              complete:function(){
                console.log("奇怪了.");
              }
            })
          }else if(res.data){
            var ttsAudioDir = wx.env.USER_DATA_PATH + "/TTSList";
            var name = ttsConfig[engineIndex].text + "." + res.ext;
            var newName = ttsUtil.newNameOfDuplicatFile(ttsList, name)
            if (name != newName) {
              ttsList = ttsList + "," + newName;
            } else {
              ttsList = ttsList + "," + name;
            }
            var scr = ttsAudioDir + "/" + newName;
            const fileManager = wx.getFileSystemManager()
            
            fileManager.writeFile({
              filePath:scr,
              data:res.data,
              encoding:'binary',
              success:function(resss){
                if(audioPlayer == null){
                  audioPlayer = wx.createInnerAudioContext();
                }
                audioPlayer.src = scr;
                console.log("src=" + scr);
                audioPlayer.play();
                wx.hideLoading();
                if(!that.data.shareOpen){
                  const eventChannel = that.getOpenerEventChannel()
                  eventChannel.emit('acceptDataFromOpenedPage', {
                    'ttsNewFile': newName
                  });     
                }               
              },
              fail:function(resss){
                wx.hideLoading();
                if(resss.errMsg == "writeFile:fail the maximum size of the file storage limit is exceeded"){
                  that.setData({
                    errMsg: that.data.i18n['localaudio.tip.maxsizelimted'],
                  })
                }else{
                  that.setData({
                    errMsg: resss.errMsg,
                  })
                }
                
              }
            });
          }
        },
        fail:function(res){
          wx.hideLoading();
          that.setData({
            errMsg: resss.errMsg,
          })
        }
      })
    }
    that.setData({
      downloadurl:"",
    })
    var config = Object.assign({}, ttsConfig[engineIndex]);
    config.person = ttsConfig[engineIndex].person[personIndex].id;
    config.speed = ttsConfig[engineIndex].speed.value;
    config.volume = ttsConfig[engineIndex].volume.value;
    config.pitch = ttsConfig[engineIndex].anchor.value;
    config.text = ttsConfig[engineIndex].text;
    config.lang = ttsConfig[engineIndex].person[personIndex].langCode;
    ttsUtil.getAudio(config);
  },
  onTTSInput: function(e) {
    var engineIndex = that.data.engineIndex;
    ttsConfig[engineIndex].text = e.detail.value;
    this.setData({
      ttsConfig: ttsConfig,
    });
  },
  onVolumeSliderChanged: function(e) {
    var engineIndex = that.data.engineIndex;
    ttsConfig[engineIndex].volume.value = e.detail.value;
    this.setData({
      ttsConfig: ttsConfig,
    });
  },
  onSpeedSliderChanged: function(e) {
    var engineIndex = that.data.engineIndex;
    ttsConfig[engineIndex].speed.value = e.detail.value;
    this.setData({
      ttsConfig: ttsConfig,
    });
  },
  onPitchSliderChanged: function(e) {
    var engineIndex = that.data.engineIndex;
    ttsConfig[engineIndex].anchor.value = e.detail.value;
    this.setData({
      ttsConfig: ttsConfig,
    });
  },
  onCopyClicked:function(e){
    var text = e.currentTarget.dataset.message
    wx.setClipboardData({
      data: text,
      success: function (res) {
        wx.showToast({
          title: that.data.i18n['tts.tip.copyok'],
        });
      }
    })
  },
  showDialog:function(visible, msg=""){
    this.setData({
      dialog:{visible:visible, msg:msg},
    })
  },
  closeDialog:function(e){
    this.setData({
      dialog:{visible:'hidden', msg:""},
    })
  },
  navback:function(e){
    app.globalData.i18n=[];
    wx.reLaunch({
      url: '../localaudio/localaudio',
    })
  }
})