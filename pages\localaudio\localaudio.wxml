<!--pages/localaudio/localaudio.wxml-->
<view hidden="{{renameObj.visible == false}}" class="zan-dialog--show">
	<view class="zan-dialog__mask" bindtap="toggleDialog" />
	<view class="zan-dialog__container">
		<view style='text-align:center; height: 100rpx;line-height: 100rpx;overflow:hidden'>{{renameObj.oldName}}</view>
		<input type='text' class="viewset2" maxlength="20" placeholder="{{i18n['localaudio.tips.newname']}}" bindinput="newNameInput" focus="{{renameObj.visible == true}}" cursor-spacing='{{space}}' value="{{renameObj.newName}}" />
		<view style="position:absolute; bottom:0;background:rgba(7,17,27,0.13);width:1rpx;height:100rpx;left:325rpx;z-index: 12;"></view>
		<view class='modal-btn-wrapper'>
			<button class="cancel-btn" bindtap="onRenameCancel">{{i18n['cancel']}}</button>
			<button class="confirm-btn" style="right:0;" bindtap="onRenameConfirm">{{i18n['confirm']}}</button>
		</view>
	</view>
</view>
<view style="opacity: 1;visibility:{{dialog.visible == true?'visible':'hidden'}}">
	<view class="weui-mask"></view>
	<view class="weui-dialog__wrp" bindtap="closeDialog">
	<view class="weui-dialog">
			<view class="weui-dialog__bd">{{dialog.msg}}</view>
			<view class="weui-dialog__ft">
					<a class="weui-dialog__btn weui-dialog__btn_primary" bindtap="closeDialog">{{i18n['confirm']}}</a>
			</view>
	</view>
	</view>
</view>

<view class="outter">

	<view data-tapid="0" class="slid-item {{currentid==0?'on':''}}" bindtap="tapBar" hidden="{{shareOpen==true}}">{{i18n['localaudio.text.import']}}</view>
	<view data-tapid="1" class="slid-item {{currentid==1?'on':''}}" bindtap="tapBar" >{{i18n['localaudio.text.tts']}}</view>
	<view data-tapid="2" class="slid-item {{currentid==2?'on':''}}" bindtap="tapBar" hidden="{{shareOpen==true}}">{{i18n['localaudio.text.recorder']}}</view>
	<view data-tapid="3" class="slid-item {{currentid==3?'on':''}}" bindtap="tapBar" hidden="{{shareOpen==true}}">{{i18n['localaudio.text.send']}}</view>

</view>
<!-- <scroll-view class="table" style="height:{{scrollHeight - 40}}px;" scroll-y>
  <block wx:for="{{currentid == '0'?fileList:(currentid == '1'?ttsList:(currentid == '2'?micRecordList:[]))}}" wx:key="id">
    <slide-delete pid="{{index}}" bindaction="onDeleteRowClicked">
      <view class="page-timr2">
          <view class="page-timr4">
              <view style="width:35px;">{{index+1}}</view>
              <view style="width:5px;">|</view>
              <view style="justify-content: center;width:100%;margin-left:5px;">{{item}}</view>
          </view>
        <image class="btnImg" src="/images/playmini.png" bindtap="play" data-index="{{index}}"></image>
      </view>
      <view class="divLine"></view>
    </slide-delete>
  </block>
</scroll-view> -->
<!-- 20200114 -->
<scroll-view class="table" style="height:{{scrollHeight - 40}}px;" scroll-y refresher-enabled='true'>
	<block wx:for="{{currentid == '0'?fileList:(currentid == '1'?ttsList:(currentid == '2'?micRecordList:[]))}}" wx:key="id">
		<scroll-view scroll-x class='scroll-container' scroll-left='{{scroll_left}}' bindscrolltoupper="onScroll2right" bindscrolltolower="onScroll2left" data-id="{{index}}" style="{{(index == clickedRow.index && currentid == clickedRow.tapid)?'height: 170px;':'height: 44px;'}}">
			<!-- <text class='content {{done1}}'>{{item}}</text> -->
			<!-- <view class="page-timr2">
            <view class="page-timr4">
                <view style="width:35px;">{{index+1}}</view>
                <view style="width:5px;">|</view>
                <view style="justify-content: center;width:100%;margin-left:5px;">{{item}}</view>
            </view>
            <image class="btnImg" src="/images/playmini.png" bindtap="play" data-index="{{index}}"></image>
        </view> -->
			<view>
				<view bindtap="onRowClicked" data-id="{{index}}" style="height: 44px;">
					<view class="content">
						<view class="page-timr4">
							<!-- <view style="width:80rpx;border-right: #DDDDDD 1rpx solid ;">{{index+1}}</view> -->
							<!-- <view style="width:4rpx;color:black;background: black;border-left: #DDDDDD 2px solid ;">|</view> -->

							<image class="btnImg" src="/images/<EMAIL>" bindtap="onPlayRowClicked" data-id="{{index}}" data-name="playbtn" hidden="{{index == playingObj.index && currentid == playingObj.tapid}}"></image>
							<image class="btnImg" src="/images/<EMAIL>" bindtap="onStopRowClicked" data-id="{{index}}" hidden="{{!(index == playingObj.index && currentid == playingObj.tapid && playingObj.state == 'playing') }}"></image>
							<view style="justify-content: center;width:540rpx;margin-left:20px;overflow:hidden">{{item}}</view>
						</view>
					</view>
					<text class='done' style="background-color: red;" data-id='{{index}}' bindtap='onDeleteRowClicked'>{{i18n['localaudio.text.delete']}}</text>
					<text class='done' style="background-color: green;" data-id='{{index}}' bindtap='onRenameRowClicked'>{{i18n['localaudio.text.rename']}}</text>
					<text class='done' style="background-color: red;" data-id='{{index}}' bindtap='onDeviceRecordRowClicked'>{{i18n['localaudio.text.inputVoice']}}</text>
				</view>
				<view style="width:100%;height: {{(index == clickedRow.index && currentid == clickedRow.tapid)?'124.7px':'0px'}};background: #EEEEEF;border-top: #DDDDDD 1px solid;border-bottom:#DDDDDD 1px solid;overflow:hidden;" hidden="{{!(index == clickedRow.index && currentid == clickedRow.tapid)}}">
					<view class="clickView" style="line-height: 54px;">
						<text style="padding-left:20px;">{{playingObj.currentTime}}</text>
						<view style="width:556rpx;height:54px;">
							<slider min="0" max="{{playingObj.duration}}" step="0.01" bindchange="onPlayProcessChanged" value="{{playingObj.currentTime}}" style="transform: translateY(50%);" activeColor="#9259B8"></slider>
						</view>
						<text style="padding-right:20px;">{{playingObj.duration}}</text>
					</view>
					<view class="clickView">
						<view style="line-height:20px;">
							<button id="recordButton" data-id='{{index}}' bindtap='onDeleteRowClicked'>
								<image  src='/images/<EMAIL>'></image>
							</button>
							<view class="clickViewText">{{i18n['localaudio.text.delete']}}</view>
						</view>
						<view style="line-height:20px;">
							<button id="recordButton" data-id='{{index}}' bindtap='onRenameRowClicked'>
								<image  src='/images/<EMAIL>'></image>
							</button>
							<view class="clickViewText">{{i18n['localaudio.text.rename']}}</view>
						</view>
						<view style="line-height:20px;">
							<button id="recordButton" data-id='{{index}}' bindtap="onDeviceRecordRowClicked">
								<image style="max-width: 60%;max-height: 60%;" src='{{deviceRecordState == 0?"/images/record.png":"/images/recording.png"}}'></image>
							</button>
							<view class="clickViewText">{{i18n['localaudio.text.inputVoice']}}</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</block>
</scroll-view>
<view style="width:100%;position:absolute; bottom:0;background:rgba(7,17,27,0.13);display: flex;">
	<button bindtap="onClearButtonClicked" style="margin-bottom:5px;margin-top:5px;height: 50px;" data-currentid="{{currentid}}">{{i18n['localaudio.text.clear']}}</button>
	<button bindtap="onImportButtonClicked" hidden="{{currentid != 0}}" style="margin-bottom:5px;margin-top:5px;height: 50px;">{{i18n['localaudio.text.import']}}</button>
	<button id="recordButton" hidden="{{currentid != 2}}" catchtouchmove='touchMoveRecordButton' catchtouchcancel='touchCancelRecordButton' bindtouchstart="touchStartRecordButton" bindtouchend='touchStopRecordButton' disabled='{{recordStatus==2}}' style="margin-bottom:5px;margin-top:5px;height: 50px;width: 50px;border-radius:50px;" type="primary">
		<view class='fa fa-microphone' style="line-height:50px;animation-play-state:paused; {{recordStatus==0||(recordStatus==3&&!isPass)?'animation: spin 2s ease-in-out infinite;-webkit-animation: spin 2s ease-in-out infinite;':(recordStatus==2?'animation: spin-running 1s   ease-in-out infinite;-webkit-animation: spin-running 1s   ease-in-out infinite;':'animation: spin2 0.8s forwards;-webkit-animation: spin2 0.8s forwards;')}}"></view>
	</button>
	<button bindtap="onTTSButtonClicked" hidden="{{currentid != 1}}" style="margin-bottom:5px;margin-top:5px;height: 50px;">{{i18n['localaudio.text.tts']}}</button>
	<button bindtap="onTransferButtonClicked" hidden="{{currentid != 3}}" style="margin-bottom:5px;margin-top:5px;height: 50px;">{{i18n['localaudio.text.send']}}</button>

	<button id="recordButton" hidden bindtap="onDeviceRecordButtonClicked" style="margin-bottom:5px;margin-top:5px;height: 50px;width: 50px;">
		<image style="transform: translate(-50%,-50%);position: absolute; top: 50%;left: 50%;max-width: 50%;max-height: 50%;-webkit-box-sizing: border-box;box-sizing: border-box;outline: none;display: block;" src='{{deviceRecordState == 0?"/images/record.png":"/images/recording.png"}}'></image>
	</button>
</view>