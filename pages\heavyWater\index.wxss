/* pages/forestindex/index.wxss */
page {
  background-color: #ffffff;
  padding-top: 20rpx;
}

.conectstate {
  position: relative;
  width: 690rpx;
  height: 380rpx;
  margin: auto;
  border-radius: 10px;
  background-color: #0C6B3A;
  padding: 42rpx 26rpx 0;
  box-sizing: border-box;
}

.goconnect {
  display: flex;
  align-items: flex-end;
  width: 100%;
  color: #FFFFFF;
  font-size: 32rpx;
  font-family: PingFangSC-medium;
}
.goconnect-btn {
  padding-bottom: 8rpx;
  height: 46rpx;
  line-height: 46rpx;
  display: flex;
  align-items: center;
  padding-left: 34rpx;
  font-size: 32rpx;
  color: #fff;
}
.goconnect-btn image {
  width: 48rpx;
  height: 48rpx;
  margin-left: 8rpx;
}
.connected{
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.blestate {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 144rpx;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, .3);
  color: rgba(16, 16, 16, 1);
  margin-top: 42rpx;
}

.leftstate {
  display: flex;
  margin-left: 27px;
  flex-direction: column;
  align-items: center;
  color: #FFFFFF;
}

.rightstate {
  display: flex;
  margin-right: 17px;
  flex-direction: column;
  align-items: center;
  color: #FFFFFF;
}
.bleconect{
   display: flex;
   align-items: center;
}

.devicedata {
  width: 690rpx;
  margin: auto;
  margin-top: 16.5px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  /* 两列，每列等宽 */
  grid-template-rows: auto auto;
  /* 两行 */
  grid-row-gap: 20px;
  /* 行列间距统一为20px */
  grid-column-gap: 23px;
}

.datastate {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 242rpx;
  border-radius: 6px;
  background-color: rgba(230, 230, 230, 0.3);
  border: 2rpx solid rgba(12,107,58,1);
  box-sizing: border-box;
}

.number {
  /* margin-top: 12px; */
  height: 78rpx;
  line-height: 78rpx;
  font-size: 56rpx;
  font-family: PingFangSC-semiBold;
  color: rgba(12, 107, 58, 1);
}

.unit {
  font-size: 28rpx;
  line-height: 40rpx;
  color: rgba(16, 16, 16, 1);
  font-family: PingFangSC-semiBold;
}

.unitname {
  font-size: 28rpx;
  color: rgba(16, 16, 16, 1);
  font-family: PingFangSC-light;
  line-height: 40rpx;
}


/**/
.throttle-box-container {
  width: 690rpx;
  margin: 100rpx auto 0;
}

.throttle-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.throttle-box-item {
  display: flex;
  align-items: center;
}

.throttle-box-item image {
  width: 60rpx;
  height: 60rpx;
  margin-right: 32rpx;
}

.throttle-box-item-right {
  width: 468rpx;
}

.throttle-box-item-right-damper {
  display: flex;
  height: 78rpx;
  border-radius: 40rpx;
  background-color: rgba(230,230,230,0.3);
  border: 2rpx solid rgba(12,107,58,1);
  overflow: hidden;
  box-sizing: border-box;
}

.throttle-box-item-right-damper view {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: rgba(16,16,16,1);
  height: 100%;
  font-family: PingFangSC-light;
  position: relative;
  border: none;
}

.throttle-box-item-right-damper view.active {
  background-color: rgba(12,107,58,1);
  color: rgba(255,255,255,1);
}


/**/
.bntbox {
  display: flex;
  align-items: center;
  width: 690rpx;
  height: 162rpx;
  margin: auto;
  margin-top: 17px;
  border-radius: 50px;
  background-color: rgba(12, 107, 58, 1);
}

.bnttype {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  width: 96%;
  height: 146rpx;
  margin: auto;
  border-radius: 50px;
  border: 1px solid rgba(230, 230, 230, 1);
  color: rgba(255, 255, 255, 1);
  font-size: 36rpx;
  font-family: PingFangSC-semiBold;
}

.segmentation {
  width: 2rpx;
  height: 78rpx;
  background-color: rgba(255, 255, 255, 1);
}

.bnt {
  width: 40%;
  height: 78rpx;
  line-height: 78rpx;
  text-align: center;
}