// pages/forestindex/index.js
const app = getApp()
var user = require('../../utils/forestble.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isconnect: false,
    mpanum: 0,
    turnnum: 0,
    celsiusnum: 0,
    hournum: 0
  },

  /**
   * 前往搜索蓝牙设备连接
   */
  goBleConnect() {
    wx.navigateTo({
      url: '../forestble/ble',
      success: () => {
        console.log('跳转蓝牙页面')
      }
    })
  },
  disconnectble() {
    var that = this
    user.clearAll();
    // 清除所有回调
    user.userGetdataCallback = null;
    user.userBleConnectFailed = null;
    user.userBleConnected = null;
    // 取消所有监听
    wx.offBLECharacteristicValueChange();
    wx.offBLEConnectionStateChange();
    wx.offAppShow();
    // 先停止搜索再断开
    wx.stopBluetoothDevicesDiscovery({
      complete: () => {
        wx.closeBLEConnection({
          deviceId: app.globalData.devId,
          complete: function (res) {
            // 重置全局变量
            app.globalData.devId = '';
            that.setData({
              isconnect: false,
              mpanum: 0,
              turnnum: 0,
              celsiusnum: 0,
              hournum: 0
            });
            console.log('已完全断开连接');
          }
        });
      }
    });
  },
  bleCallBack() {
    console.log('点击设备返回开始连接')
    wx.showLoading({
      title: '连接中...',
    })
    var that = this
    // user.connectBle(app.globalData.devId);
    user.reconnectBle(app.globalData.devId);
    user.userBleConnectFailed = res => {
      console.log("连接失败");
      wx.showToast({
        title: '连接失败!',
        icon: 'error',
        duration: 2000
      })
    }
    user.userBleConnectState = res => {
      console.log("连接状态", res);
      this.setData({
        isconnect: res.connected
      })
    }
    user.userBleConnected = res => {
      console.log("连接成功.", res)
      wx.hideLoading()
      this.setData({
        isconnect: true
      })
    }
    user.userGetdataCallback = res => {
      console.log("获取下位机回复数据", res)
      that.setData({
        mpanum: res.pressure.toFixed(2), // 水压，保留2位小数
        turnnum: res.speed,              // 转速
        celsiusnum: res.temperature.toFixed(1), // 温度，保留1位小数
        hournum: res.workingHours.toFixed(1),  // 工作时长，保留1位小数
      })
    }
  },
  // 按下启动设备
  startDevicePress() {
    if (!this.data.isconnect) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return
    }
    user.writeData(1, 0) // 启动标志位为1，停止为0
  },
  // 松开启动设备
  startDeviceRelease() {
    if (!this.data.isconnect) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return
    }
    user.writeData(0, 0) // 启动标志位为1，停止为0
  },
  // 停止设备
  stopDevicePress() {
    if (!this.data.isconnect) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return
    }
    user.writeData(0, 1) // 启动标志位为0，停止为1
  },
  stopDeviceRelease() {
    if (!this.data.isconnect) {
      wx.showToast({
        title: '请先连接设备',
        icon: 'none'
      })
      return
    }
    user.writeData(0, 0) // 启动标志位为0，停止为1
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('设备详情页面信息', options)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时检查连接状态
    if (app.globalData.devId && this.data.isconnect) {
      this.setData({
        isconnect: true
      })
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    wx.offAppShow();
    // 调用清理方法
    user.clearAll();
    user.userGetdataCallback = null;
    wx.offBLECharacteristicValueChange();
    wx.offBLEConnectionStateChange();
    wx.stopBluetoothDevicesDiscovery({
      complete: () => {
        if (app.globalData.devId) {
          wx.closeBLEConnection({
            deviceId: app.globalData.devId,
            complete: () => {
              app.globalData.devId = '';
            }
          });
        }
      }
    });
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})