/* pages/accordion/accordion.wxss */
page{
   height:100%                        
}

.imagesize{
 display:flex;       
 height: 100%;            
 justify-content: space-between; 
 align-items: center;        
}
.imagesize image { 
  width:400rpx;
  height:400rpx;
  }

.page {
    height:100rpx;
  display: flex;
  color: white;
  justify-content: space-between;
  align-items: center;
  font-size: 30;
}

.page1 {
    height:100rpx;
  display: flex;
  color: rgb(3, 0, 0);
  justify-content: center;
  align-items: center;
  margin: 10rpx;
}

.page2 {
    height:100rpx;
  display: flex;
  color: white;
  justify-content: center;
  align-items: center;
  font-size: 30;
}

.page3 {
    width: 40rpx;
    height:100rpx;
  display: flex;
  color: white;
  justify-content: right;
  align-items: center;
    margin: 10rpx;

}

.page-btn {
    height:50px;
  display: flex;
  
}

.divLine{
    background: #E0E3DA;
    height: 3rpx;
}

.btnImg {

  width: 40rpx;
  height: 40rpx;
}

.btnImg1 {

  width: 46rpx;
  height: 40rpx;
}

.page-timer {
    height: 240rpx;
    color: black;
    font-size: 30;
    margin: 20rpx;
    border-radius: 10rpx;
}

.bg-gray{
    background: #dddddd;
}

.bg-green{
    background: #00FF00;
}

.page-timr1{
    width: 400rpx;
    font-size: 40rpx;
    margin-left: 10rpx;
}

.page-timr2{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10rpx;
}

.page-timr3{
    width: 60rpx;
    font-size: 40rpx;
    margin-right: 0px;
    border-top-right-radius: 10rpx;
}

.page-timr4{

    font-size: 30rpx;
    margin-left: 10rpx;
}

.page-timr5{
    font-size: 30rpx;
    margin-right: 40rpx;
}

.page-timr6{
    font-size: 30rpx;
    margin-right: 20rpx;
}

.page-timr7{
    font-size: 30rpx;
    margin-right: 380rpx;
}

.page-timr-1{
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.viewset4{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100rpx;
    font-size: 50rpx;
    /* background-color: #808080; */
}

.viewset3{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 120rpx;
    font-size: 50rpx;
    /* background-color: #808080; */
}


.view{
    box-sizing: border-box;
}
.item-box{
    width: 700rpx;
    margin: 0 auto;
    padding:40rpx 0;
}
.items{
    width: 100%;
}
.item{
    position: relative;
    border-top: 2rpx solid #eee;
    height: 120rpx;
    line-height: 120rpx;
    overflow: hidden;
    
}
.item:last-child{
    border-bottom: 2rpx solid #eee;
}
.inner{
    position: absolute;
    top:0;
}
.inner.txt{
    background-color: #fff;
    width: 100%;
    z-index: 5;
    padding:0 10rpx;
    transition: left 0.2s ease-in-out;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}
.inner.del{
    background-color: #e64340;
    width: 180rpx;text-align: center;
    z-index: 4;
    right: 0;
    color: #fff
}
.item-icon{
    width: 64rpx;
    vertical-align: middle;
    margin-right: 16rpx
}
.thumb{
    width: 400rpx;
    height: 400rpx;
    -webkit-overflow-scrolling: touch;
    overflow: scroll;
}
