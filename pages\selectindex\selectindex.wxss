/* pages/selectindex/selectindex.wxss */
page{
  background-color: #ffffff;
  height: 100vh;
}

.selectindex-container {
  display: flex;
  height: 100%;
}

.select-types {
  width: 240rpx;
  min-width: 240rpx;
  background-color: #F8F8F8;
  padding-top: 6rpx;
}

.select-types-item {
  height: 132rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  border-radius: 10rpx;
}

.select-types-item.active {
  background-color: #fff;
}

.select-types-item.active .select-types-item-text {
  color: #0C6B3A;
}

.select-types-item-icon {
  width: 56rpx;
  height: 56rpx;
  min-width: 56rpx;
  margin-right: 24rpx;
}

.select-types-item-text {
  line-height: 30rpx;
  color: rgba(63,61,61,1);
  font-size: 28rpx;
}

.select-types-content {
  flex: 1;
  padding: 66rpx 22rpx 0 10rpx;
}

.select-types-content-title {
  height: 40rpx;
  line-height: 40rpx;
  color: rgba(5,5,5,1);
  font-size: 28rpx;
  margin-bottom: 38rpx;
}

.select-types-content-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 102rpx;
  border-radius: 10rpx;
  background-color: rgba(127,177,110,0.07);
  padding: 0 16rpx 0 34rpx;
  margin-bottom: 20rpx;
  color: rgba(5,5,5,1);
  font-size: 28rpx;
}

.select-types-content-item .select-types-content-item-text {
  color: rgba(12,107,58,1);
}

.select-types-content-item.disabled {
  background-color: rgba(241,241,241,1);
}

.select-types-content-item.disabled .select-types-content-item-text {
  color: rgba(5,5,5,1);
}

.select-types-content-item-text {
  line-height: 40rpx;
  color: rgba(5,5,5,1);
  font-size: 28rpx;
}

.select-types-content-item-icon {
  width: 40rpx;
  height: 40rpx;
}


/* 弹窗 */
.disabled-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.2);
}

.disabled-dialog-content {
  width: 526rpx;
  height: 420rpx;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 52rpx 62rpx;
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.disabled-dialog-content-icon {
  width: 70rpx;
  height: 70rpx;
  margin-bottom: 66rpx;
}

.disabled-dialog-content-title {
  line-height: 40rpx;
  color: rgba(5,5,5,1);
  font-size: 28rpx;
  margin-bottom: 42rpx;
}

.disabled-dialog-content-btn {
  width: 402rpx;
  height: 102rpx;
  line-height: 40rpx;
  border-radius: 52rpx;
  background-color: rgba(12,107,58,1);
  color: rgba(255,255,255,1);
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
