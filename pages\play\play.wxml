<!--pages/play/play.wxml-->
<!--pages/trigger/trigger.wxml-->

<view class="page-timr2">
    <view class="page-timr4">{{text1}}</view>
    <switch checked="{{checked1}}" class="page-timr6" bindchange="change1"></switch>
</view>

<view class="divLine"></view>

<view class="page-timr2">
    <view class="page-timr4">{{text2}}</view>
    <switch checked="{{checked2}}" class="page-timr6" bindchange="change2"></switch>
</view>

<view class="divLine"></view>

<view class="page-timr2">
    <view class="page-timr4">{{text3}}</view>
    <switch checked="{{checked3}}" class="page-timr6" bindchange="change3"></switch>
</view>

<view class="divLine"></view>

<view class="page-timr2">
    <view class="page-timr4">{{text4}}</view>
    <switch checked="{{checked4}}" class="page-timr6" bindchange="change4"></switch>
</view>

<view class="divLine"></view>

<view class="page-timr2">
    <view class="page-timr4">{{text5}}</view>
    <switch checked="{{checked5}}" class="page-timr6" bindchange="change5"></switch>
</view>

<view class="divLine"></view>

<view class="page-timr2">
    <view class="page-timr4">{{text6}}</view>
    <switch checked="{{checked6}}" class="page-timr6" bindchange="change6"></switch>
</view>

<view class="divLine"></view>


<view class="page-timr1" bindtap="synchronize">
    <image wx:if="{{isChanged}}" class="btnImg" src="/images/synchronized.png"></image>
    <image wx:else class="btnImg" src="/images/synchronize.png"></image>
    <view class="page-timr4">{{text8}}</view>
</view>
