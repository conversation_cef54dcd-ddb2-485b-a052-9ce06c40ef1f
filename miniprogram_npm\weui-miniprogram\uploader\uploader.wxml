<view class="weui-uploader {{extClass}}">
    <view class="weui-uploader__hd">
      <view class="weui-uploader__overview">
          <view class="weui-uploader__title">{{title}}</view>
          <view class="weui-uploader__info" wx:if="{{maxCount > 1}}">{{currentFiles.length}}/{{maxCount}}</view>

      </view>
        <view wx:if="{{tips}}" class="weui-uploader__tips">{{tips}}</view>
        <view wx:else><slot name="tips"></slot></view>
    </view>
    <view class="weui-uploader__bd">
        <view class="weui-uploader__files">
            <block wx:for="{{currentFiles}}" wx:key="*this">
                <view wx:if="{{item.error}}" data-index="{{index}}" bindtap="previewImage" class="weui-uploader__file weui-uploader__file_status">
                    <image class="weui-uploader__img" src="{{item.url}}" mode="aspectFill" />
                    <view class="weui-uploader__file-content">
                        <icon type="warn" size="23" color="#F43530"></icon>
                    </view>
                </view>
                <view wx:elif="{{item.loading}}" data-index="{{index}}" bindtap="previewImage" class="weui-uploader__file weui-uploader__file_status">
                    <image class="weui-uploader__img" src="{{item.url}}" mode="aspectFill" />
                    <view class="weui-uploader__file-content">
                    <view class="weui-loading"></view>
                    </view>
                </view>
                <view wx:else class="weui-uploader__file" data-index="{{index}}" bindtap="previewImage">
                    <image class="weui-uploader__img" src="{{item.url}}" mode="aspectFill" />
                </view>
            </block>
        </view>
        <view wx:if="{{currentFiles.length < maxCount}}" class="weui-uploader__input-box">
            <view class="weui-uploader__input" bindtap="chooseImage"></view>
        </view>
    </view>
</view>
<mp-gallery class="gallery" hide-on-click="{{true}}" show-delete="{{showDelete}}" show="{{showPreview}}" binddelete="deletePic" img-urls="{{previewImageUrls}}" current="{{previewCurrent}}"></mp-gallery>
