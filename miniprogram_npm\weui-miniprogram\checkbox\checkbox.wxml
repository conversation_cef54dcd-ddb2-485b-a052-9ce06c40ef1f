<mp-cell
  has-footer="{{!multi}}"
  has-header="{{multi}}"
  bindtap="checkedChange"
  ext-class="weui-check__label {{outerClass}} {{extClass}} {{!multi ? '^weui-cell_radio' : '^weui-cell_checkbox'}}">

  <view slot="icon" wx:if="{{multi}}">
    <checkbox value="{{value}}" checked="{{checked}}" disabled="{{disabled}}" color="{{color}}" class="weui-check">
    </checkbox>
    <!-- 未勾选 -->
    <icon class="weui-icon-checked"></icon>
  </view>
  <view>{{label}}</view>
  <view slot="footer" wx:if="{{!multi}}">
    <radio value="{{value}}" checked="{{checked}}" disabled="{{disabled}}" color="{{color}}" class="weui-check"></radio>
    <!-- 已勾选 -->
    <icon class="weui-icon-checked"></icon>
  </view>
</mp-cell>
