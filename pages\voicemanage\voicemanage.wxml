<!--pages/voicemanage/voice.wxml-->
<view class="table">
  
  <block wx:for="{{sData}}" wx:key="{{item.id}}">
    <!--暂时关闭删除指定文件功能,下位机未实现. <slide-delete pid="{{item.id}}" bindaction="handleSlideDelete"> -->

    <view class="page-timr2">
        <view class="page-timr4">
            <view class="td">{{index+1}}</view>
            <view class="td"> | </view>
            <view class="td">{{item.name}}</view>
        </view>
      
      <image class="btnImg" src="{{playurl}}" bindtap="playsong" data-index="{{index}}"></image>
    </view>
    <view class="divLine"></view>

    <!-- </slide-delete> -->
  </block>

  <view style="width:100%;height:150rpx;"></view>
</view>

<view class="page-timr8" style="z-index: 2;">
    <button class="btnset" bindtap="dellast">{{lastsong}}</button>
    <button class="btnset" bindtap="delall">{{allsong}}</button>
</view>