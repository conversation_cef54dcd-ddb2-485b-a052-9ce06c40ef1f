/* pages/localaudio/localaudio.wxss */
page {
  overflow-y: hidden;
}
.outter{

  display: flex;
 
  width: 100%;
 
  justify-content: space-around;
 
  /* border-top: 2rpx solid black; */
  box-sizing: content-box;
  background: #606060;

  height: 40px;
 }
 
 .slid-item{
 
  margin-left: 45rpx;
 
  margin-right: 45rpx;

  color: white;

  line-height:40px;
  border-radius: 1px;
 }
 
 .on{
 
  border-top: 2px solid white;
  line-height:36px;/*避免文字垂直变化*/
 }
 
/*动画生效*/
@keyframes spin {
  0% {
     transform: scale(.9);
    opacity: .2;

  }
  50% {
    opacity: 1;
               transform: scale(1);

  }
  100% {
    opacity: .2;
     transform: scale(.9);

  }
}


@keyframes  spin-running {
  0% {
     transform: scale(.9);
    opacity: .2;
        transform:rotateZ(0deg);

  }
  50% {
    opacity: 1;
               transform: scale(1);
         transform:rotateZ(160deg);

  }
  100% {
    opacity: .2;
     transform: scale(.9);
         transform:rotateZ(360deg);

  }
}


@keyframes spin2 {
  0% {
     transform: scale(0.9);
    opacity: 1;
  }

  100% {
    opacity: .2;
     transform: scale(2);
  }
}


 @keyframes spin3 {
  0% {
     transform: scale(0.9);
    opacity:.3;
    
  }

  100% {
    opacity:1;
     transform: scale(1);
  }
}



 @-webkit-keyframes spin {
  0% {
     transform: scale(.9);
    opacity: .2;

  }
  50% {
    opacity: 1;
               transform: scale(1);

  }
  100% {
    opacity: .2;
     transform: scale(.9);

  }
}


@-webkit-keyframes  spin-running {
  0% {
     transform: scale(.9);
    opacity: .2;
        transform:rotateZ(0deg);

  }
  50% {
    opacity: 1;
               transform: scale(1);
         transform:rotateZ(160deg);

  }
  100% {
    opacity: .2;
     transform: scale(.9);
         transform:rotateZ(360deg);

  }
}


@-webkit-keyframes spin2 {
  0% {
     transform: scale(0.9);
    opacity: 1;
  }

  100% {
    opacity: .2;
     transform: scale(2);
  }
}


 @-webkit-keyframes spin3 {
  0% {
     transform: scale(0.9);
    opacity:.3;
    
  }

  100% {
    opacity:1;
     transform: scale(1);
  }
}
.table {
  border: 0px solid darkgray;
}
.btn {
  margin-top: 10rpx;
  font-size: 20px;
}
#recordButton {
  width: 40px;
  height: 40px;
    border-radius:40px;
  border: 0;

}
#recordButton::after {
  border: 0;

}

#recordButton view{
font-size:30px;
line-height:40px;
border: 0;
color:#ffffff;

}
.tr {
  display: flex;
  width: 100%;
  justify-content: center;
  height: 3rem;
  align-items: center;
}
.td {
    width:40%;
    justify-content: center;
    text-align: center;
}
.bg-w{
  background: snow;
}
.bg-g{
  background: #E6F3F9;
}
.th {
  width: 40%;
  justify-content: center;
  background: #3366FF;
  color: #fff;
  display: flex;
  height: 3rem;
  align-items: center;
}
.btnImg {
  width: 68rpx;
  height: 66rpx;
  background: white;
  /* margin: 10rpx; */
  /* z-index: 10; */
}
.page-timr4{
    /* width: 100%; */
    display: flex;
    /* justify-content: space-between; */
    /* justify-content: center; */
    align-items: center;
    /* margin-left: 5px; */
}
.page-timr2{
    font-size: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px;
}

.divLine{
    background: #E0E3DA;
    height: 3rpx;
}

.page-timr8{
    width: 100%;
    height: 50px;
    font-size: 20px;
    position: fixed;
    display: flex;
    justify-content: space-between;
    margin: 0px;
    bottom: 20px;
}

.btnset {
  width: 49%;
  background: #DDDDDD;
}
/* 20200114 */
::-webkit-scrollbar{

  width: 0;

  height: 0;

  color: transparent;

}

.scroll-container{

  height: 44px;

  /* background-color: floralwhite; */

  margin-top: 0rpx;

  display: flex;

  flex-direction: row;

  align-items: center;

  white-space: nowrap;

  width: 100%;

  border-bottom: #DDDDDD 1rpx solid ;
  
}

.content{

  padding-left: 20rpx;

  display: inline-block;

  line-height: 44px;

  font-size: 40rpx;

  width: 750rpx;

}


.done{

  display: inline-flex;

  width: 120rpx;

  height: 44px;

  line-height: 44px;

  align-items: center;

  justify-content: center;  

  color: white;
  vertical-align:top;
}

.done1{

  text-decoration: line-through;

  color: gainsboro;

}

.clickView{
  display: flex;
  line-height: 44px;
  justify-content: space-around;
}
.clickView image{
  transform: translate(-50%,-50%);
  position: absolute; 
  top: 50%;
  left: 50%;
  max-width: 100%;
  max-height: 100%;
}
.clickViewText{
  vertical-align: text-top;
  text-align:center;
}
.viewset2{
  text-align: center;
  border: 1rpx solid rgba(7,17,27,0.1);
  line-height: 100rpx;
  height: 100rpx;
  margin: 20rpx;
  margin-left: 60rpx;
  margin-right: 60rpx;
}

.zan-dialog__mask {
position: fixed;
top: 0;
left: 0;
right: 0;
bottom: 0;
z-index: 10;
/* 设置阴影半透明背景如： background: rgba(0, 0, 0, 0.4); */
background: rgba(0, 0, 0, 0.4);
display: none;
}

.zan-dialog__container {
position: fixed;
top: 200rpx;
width: 650rpx;/*弹窗布局宽*/
height: 350rpx;/*弹窗布局高，与下面弹出距离transform有关*/
margin-left: 50rpx;
margin-right: 50rpx;
background: #f8f8f8;
transform: translateY(500%);/*弹框弹出距离，与弹框布局高度有关，如300%表示弹起距离为3倍弹窗高度*/
transition: all 0.4s ease;
z-index: 12;
border-radius: 20rpx;
/*box-shadow: 0px 3px 3px 2px gainsboro;弹框的悬浮阴影效果，如不需要可注释该行*/
}

.zan-dialog--show .zan-dialog__container {
transform: translateY(0);
}

.zan-dialog--show .zan-dialog__mask {
display: block;
}

.modal-btn-wrapper{
display: flex;
flex-direction: row;
height: 100rpx;
line-height: 100rpx;
}

.cancel-btn, .confirm-btn{
flex: 1;
height: 100rpx;
line-height: 100rpx;
text-align: center;
width: 324.5rpx;
position:absolute; bottom:0;
border-top: 2rpx solid rgba(7,17,27,0.1); 
border-top-left-radius: 0;
border-top-right-radius: 0;
/* font-size: 32rpx; */
}

.cancel-btn{
/* border-right: 2rpx solid rgba(7,17,27,0.1); */
border-bottom-left-radius: 20rpx;
border-bottom-right-radius: 0;
}
.confirm-btn{
/* border-right: 2rpx solid rgba(7,17,27,0.1); */
border-bottom-right-radius: 20rpx;
border-bottom-left-radius: 0;
color: green;
}
.cancel-btn::after{
/* border: none; */
border-top: none;
border-right: none;
border-top-left-radius: 0;
border-top-right-radius: 0;
border-bottom-right-radius: 0;
}
.confirm-btn::after{
/* border: none; */
border-top: none;
border-left: none;
/* border-right: 1rpx solid rgba(7, 17, 27, 0.1); */
border-top-left-radius: 0;
border-top-right-radius: 0;
border-bottom-left-radius: 0;
}  
.btnImg3 {
  width: 41px;
  height: 50px;
  /* margin: 5px; */
  line-height:50px;
}