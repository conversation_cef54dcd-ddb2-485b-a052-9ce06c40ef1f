<!--pages/voicename/voicename.wxml-->
<view>
    <view class="pageset" >
        <!-- <view class="viewset1" bindtap="enter">{{cancel}}</view> -->
        <view class="viewset1" bindtap="setdefault">{{cancel}}</view>
        <view class="viewset1" bindtap="enter">{{enter}}</view>
    </view>

    <view class="divLine"></view>

    <view class="pageset1" >
        <view class="{{class1}}" bindtap="procbind1">
            <view class="viewset1">{{text1}}</view>
        </view>

        <view class="{{class2}}" bindtap="procbind2">
            <view class="viewset1">{{text2}}</view>
        </view>
        
    </view>

    <view class="divLine"></view>

<!-- all -->
    <view wx:if="{{choiced}}">
        <radio-group bindchange="radiochange">
        <view class="page-timr6">
        <switch class="page-timr5" checked="{{isLoop}}" bindchange="allLoop"></switch>
        <view>{{i18n["timer.loostr.Allloop"]}}</view>
    </view>
    <view class="divLine"></view>

        </radio-group>
    </view>
<!-- single -->
    <view wx:else>
    <view class="page-timr6">
        <switch class="page-timr5" checked="{{isLoop}}" bindchange="singleLoop"></switch>
        <view>{{i18n["timer.loostr.Singleloop"]}}</view>
    </view>
    <view class="divLine"></view>
    
        <view class="table">
        
  <radio-group bindchange="radiochange">
  <block wx:for="{{sData}}">
    <view class="page-timr2">
        <view class="page-timr4">
            <view class="td">{{index+1}}</view>
            <view class="td"> | </view>
            <view class="td">{{item}}</view>
        </view>   
      <image class="btnImg" src="{{playurl}}" bindtap="playsong" data-index="{{index}}"></image>
      <radio value='{{index}}' checked="{{songnum == index}}"></radio>
    </view>
    <view class="divLine"></view>
  </block>
  </radio-group>

  <view style="width:100%;height:80px;"></view>
  
</view>
    </view>

    <view class="bottom" style="width:100%;height:80px;background-color:#f0f0f0;position:fixed;bottom:0;z-index: 2;">
    <view class="page-timr3">
    
    <view>
        <image class="btnImg2" src="/images/voice-3.png"></image>
        <!-- <image wx:elif="{{volumevalue > 10}}" class="btnImg2" src="/images/voice-2.png"></image>
        <image wx:elif="{{volumevalue > 0}}" class="btnImg2" src="/images/voice-1.png"></image>
        <image wx:else class="btnImg2" src="/images/voice-0.png"></image> -->
    </view>

    <view>
        <slider  bindchange="volchange" class="sliderset" show-value="true" max="30" value="{{volume}}"></slider>
    </view>
</view>
</view>

</view>