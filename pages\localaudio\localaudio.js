// pages/localaudio/localaudio.js
const util = require('../../utils/util.js');
const app = getApp();
var recorderManager;
var tempFilePath;/**录音的临时文件 */
var that;
var startTime = 0;
var maxLen = 120;/**录音时长最长时间s */
var minLen = 1;/**录音时长最短时间s */
var timer;/**录音定时器 */
var fileManager;
var audioPlayer = null;
var user = require('../../utils/ble.js');
var eventChannel;
const langData = require('../../langData.js');
Page({

  /**
   * 页面的初始数据
   */
  data: {
    i18n:[],
    fileList:[],/**导入文件列表 */
    ttsList:[],/**合成文件列表 */
    micRecordList:[],/**手机录音文件列表 */
    audioDir:"",/**导入文件夹 */
    ttsDir:"",/**合成文件夹 */
    micRecordDir:"",/**手机录音文件夹 */
    scrollHeight:'auto',
    recordStatus: 0,/**0初始,1按住,2分析中,3停止*/
    startPoint: [0, 0],/**用于判断用户是否上滑取消录音 */
    currentid:'0',
    scroll_left:'0rpx',
    renameObj:{"tapid":-1,"index":-1,"oldName":"当前文件名","newName":"新文件名","visible":false},
    playingObj:{"tapid":0,"index":-1,"name":"","state":"","duration":0,"currentTime":0},//state:start,playing,stop,ended,recording,
    deviceRecordState:0,
    clickedRow:{"tapid":-1,"index":-1},
    dialog:{visible:'hidden', msg:""},
    shareOpen:false,//是否分享直接进入本界面的.
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    that = this;
    var scrollHeight = wx.getSystemInfoSync().windowHeight - 62;
    if(app.globalData.i18n.length == 0){
      app.globalData.i18n = langData.data.i18n;
      this.setData({
        shareOpen:true,
      })
    }
    this.setData({
      scrollHeight:scrollHeight,
      i18n: app.globalData.i18n,
      deviceRecordState:options.state,
    })
    this.configFileDir();
    this.configRecorder();
    this.configAudioPlay();

    if(this.data.shareOpen == false){
      //设备录音事件回调
      eventChannel = this.getOpenerEventChannel();
      eventChannel.on('deviceRecordCallBack',function(data){
        // wx.hideLoading({
        //   complete: (res) => {},
        // })
        console.log("传递参数"+data.state);
        that.setData({
          deviceRecordState:data.state,
        });

        if(data.state == 1){
          //正在录音,可以自动播放当前音乐了.
          //bug,无法判断是否未连接音频蓝牙,如果未蓝牙音频蓝牙,则会进行播放
          //改成录音?改成录音会导致播放试听步骤变慢,必须有蓝牙返回才播放.
          //在准备录音的状态下,点击录音,
          if(that.data.playingObj.index >= 0 && that.data.playingObj.state == 'recording'){
            var index = that.data.playingObj.index;
            var currentid = that.data.currentid;
            that.playAtIndex(currentid,index);
          }
        }else //if(that.data.playingObj.index == -1)
        {
          that.onStopRowClicked();
        }
      });
    }else{
      that.setData({
        currentid:'1',
      })
    }
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    if(this.data.deviceRecordState == 1){
      eventChannel.emit('onDeviceRecord', {"test":"界面隐藏就停止录音"});
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
      if(audioPlayer != null){
        audioPlayer.destroy();
        audioPlayer = null;
      }
      if(eventChannel != null){
        eventChannel.off("deviceRecordCallBack");
        eventChannel.emit('onPageUnload', {"msg":"退出界面"});        
        eventChannel = null;
      }
      
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },
  onScroll2left:function(e){
    console.log("左滑动"+e.currentTarget.dataset.id);
  },
  onScroll2right:function(e){
    console.log("右滑动"+e.currentTarget.dataset.id);
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function (e) {
    console.log("分享")
    
    // return {
    //   title: '自定义转发标题',
    //   // path: '/page/user?id=123',
    //   imageUrl:this.audioDir+"/"+"333(1).bin"
    // }
  },
  onImportButtonClicked:function(e){    
    var audioDir = this.data.audioDir;
    try {
      fileManager.accessSync(audioDir);
      console.log(audioDir+"目录已经存在")
    } catch (error) {
      fileManager.mkdirSync(audioDir,true);
    }

    var fileList = that.data.fileList;

    wx.chooseMessageFile({
      count:100,
      type:'file',
      extension:['wav','mp3','m4a','mid','wma','avi',"aac"],
      success: function (res) {
        console.log(res);        
        res.tempFiles.forEach(element => {    

          var destName = that.newNameOfDuplicatFile(fileList,element.name);
          var destPath = audioDir+"/"+ destName;

          fileManager.copyFile({
            srcPath:element.path,
            destPath:destPath,
            success:function(copyRes){
              console.log(copyRes);  
              if(fileList.indexOf(destName,0) >= 0){
                
              }else{
                fileList.push(destName);
              }
            },
            fail:function(copyRes){
              console.log(copyRes.errMsg);
              if(copyRes.errMsg == "copyFile:fail the maximum size of the file storage limit is exceeded"){
                // wx.showToast({
                //   title: copyRes.errMsg,
                // })
                that.showDialog(true,that.data.i18n['localaudio.tip.maxsizelimted']);
              }else{
                that.showDialog(true,copyRes.errMsg);
              }
              
            },
            complete:function(copyRes){
              fileList = fileList.sort(function(a,b){
                return a.localeCompare(b,'zh-CN');
              })
              that.setData({
                fileList:fileList,
              })
            }
          })
        });
      },
      fail: function (err) {
        console.log(err);
      },
    });
  },
  onClearButtonClicked:function(e){
    //清空文件列表
    console.log("清空"+e.currentTarget.dataset.currentid);
    var currentid = e.currentTarget.dataset.currentid;
    var fileList = this.fileListOfCurrentid(currentid);
    var dir = this.dirOfCurrentid(currentid);
    
    fileManager.rmdir({
      dirPath:dir,
      recursive:true,
      success:function(res){
        that.setFileListAtId(currentid,[]);
        console.log("清空成功");
      },
      fail:function(res){
        console.log("清空失败");
      },
      complete:function(res){

      }
    })
  }, 
  onDeviceRecordButtonClicked:function(e){
    //发起设备录音.
    // wx.showLoading({
    //   title: app.globalData.i18n["control.tips.sending"],
    //     mask: true
    // })
    console.log("设备录音");       
    eventChannel.emit('onDeviceRecord', {"test":"点击播录"});
    
    return;

    if (that.data.isDeviceRecording) {    
        that.data.isDeviceStopRecord = true
        var buffer = [0xA7, 0x02, 0x00, 0xA9];
        user.writeData(buffer)
        time = 0;
        var interval = setInterval(function () {
          if (that.data.isDeviceStopRecord && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                time++;
                if (time < 3) {
                    user.writeData(buffer)
                }
                else {
                    that.data.isDeviceStopRecord = false
                    clearInterval(interval);
                    wx.hideLoading();
                  that.showSendingFailed();
                }
            }
            else {
                clearInterval(interval);
            }

        }, 5000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })
    } 
    else 
    {
        that.data.isDeviceStartRecord = true
        var buffer = [0xA7, 0x01, 0x00, 0xA8];
        user.writeData(buffer)
        time = 0;
        var interval = setInterval(function () {
          if (that.data.isDeviceStartRecord && getCurrentPages().reverse()[0].route == that.data.currentPage) {
                time++;

                if (time < 3) {
                    user.writeData(buffer)

                }
                else {
                    that.data.isDeviceStartRecord = false
                    clearInterval(interval);
                    wx.hideLoading();
                    that.showSendingFailed();
                }
            }
            else {
                clearInterval(interval);
            }

        }, 6000)

        wx.showLoading({
          title: app.globalData.i18n["control.tips.sending"],
            mask: true
        })
    }
      

  
  },
  onDeviceRecordRowClicked:function(e){
    //发起设备录音.
    // wx.showLoading({
    //   title: app.globalData.i18n["control.tips.sending"],
    //     mask: true
    // })
    console.log("设备录音");
    var index = e.currentTarget.dataset.id;
    var currentid = this.data.currentid;
    var timeout = 200;
    if(this.data.deviceRecordState == 1){
      //设备正在录音状态,停止录音,收到停止录音后,再停止播放;
      eventChannel.emit('onDeviceRecord', {"test":"停止录音,收到停止录音后,再停止播放"});
      // timeout = 500;
      // setTimeout(() => {
      //   that.playAtIndex(currentid,index);
      // }, timeout);    
    }else if (this.data.playingObj.index >=0 && ( this.data.playingObj.state == 'playing' ||  this.data.playingObj.state == 'recording')){
      //设备不在录音状态,正在播放,开始录音,但不干涉当前的播放;
      eventChannel.emit('onDeviceRecord', {"test":"设备不在录音状态,正在播放,开始录音,但不干涉当前的播放"});  
    }else{
      //this.data.playingObj = {"tapid":currentid,"index":index,"name":name,"state":"recording"};
      //设备不在录音状态,不在播放,
      var name = this.nameOfCurrentidAndIndex(currentid,index)
      this.data.playingObj.tapid = currentid;
      this.data.playingObj.index = index;
      this.data.playingObj.name = name;
      this.data.playingObj.state = "recording";
      eventChannel.emit('onDeviceRecord', {"test":"点击录音并且播放"});   
    
      
    }
     
    return;
  },
  onTransferButtonClicked:function(e){
    console.log("点击数传");
  },
  onRowClicked:function(e){  
    console.log("点击行,"+ e.currentTarget.dataset.id,this.data.currentid);
    if(this.data.playingObj.index >= 0 && e.target.dataset.name != "playbtn"){
      //正在播放,不可换行;
      return;
    }else if(e.target.dataset.name != "playbtn"){
      that.data.playingObj.duration = 1;
      that.data.playingObj.currentTime = 0;
      that.setData({
        playingObj:that.data.playingObj,
      })
      that.data.playingObj.duration = 0;
      that.setData({
        playingObj:that.data.playingObj,
      })
    }

    var index = e.currentTarget.dataset.id;
    var currentid = this.data.currentid;

    if(index == this.data.clickedRow.index && currentid == this.data.clickedRow.tapid 
      && this.data.playingObj.index == -1){
      this.setData({
        clickedRow:{"index":-1,"tapid":-1}
      });
    }else{
      this.setData({
        clickedRow:{"index":index,"tapid":currentid}
      });
    }
   
  },
  onPlayRowClicked:function(e){        
    console.log("播放"+e.currentTarget.dataset.id);
    var index = e.currentTarget.dataset.id;
    var currentid = this.data.currentid;
    if(this.data.playingObj.state == "playing"){
      audioPlayer.stop();
      setTimeout(() => {
        this.playAtIndex(currentid,index);
      }, 500);
    }else{
      this.playAtIndex(currentid,index);
    }
  },
  
  playAtIndex:function(currentid,index){    
    var name = this.nameOfCurrentidAndIndex(currentid,index);
    var scr = this.dirOfCurrentid(currentid) + "/" + name;
    audioPlayer.src = scr;
    
    setTimeout(() => {
      // audioPlayer.currentTime;
      console.log(audioPlayer.currentTime);
      // audioPlayer.onCanplay(()=>{
      //   that.data.playingObj.duration = audioPlayer.duration;
      //   that.setData({
      //     playingObj:that.data.playingObj,
      //   })
      // });
      // audioPlayer.onTimeUpdate(() => {
      //   //console.log("总时长" + audioPlayer.duration)
      //   //console.log("当前播放进度" + audioPlayer.currentTime)
      //   that.data.playingObj.duration = audioPlayer.duration;
      //   that.data.playingObj.currentTime = audioPlayer.currentTime;
      //   that.setData({
      //     playingObj:that.data.playingObj,
      //   })
      // })
      audioPlayer.play();
    }, 100);
    //this.data.playingObj = {"tapid":currentid,"index":index,"name":name,"state":"start"};
    this.data.playingObj.tapid = currentid;
    this.data.playingObj.index = index;
    this.data.playingObj.name = name;
    this.data.playingObj.state = "start";
    this.data.playingObj.duration = 0;
    this.data.playingObj.currentTime = 0;
    wx.showLoading({
      title: that.data.i18n["voicemanager.playing"],
      mask:false,
    });
  },
  onStopRowClicked:function(e){
    audioPlayer.stop();
    //this.data.playingObj = { "tapid": -1, "index": -1, "name": "", "state": "ended" };
    this.data.playingObj.tapid = -1;
    this.data.playingObj.index = -1;
    this.data.playingObj.name = "";
    this.data.playingObj.state = "ended";
    wx.hideLoading({
      complete: (res) => {},
    })
  },
  onDeleteRowClicked:function(e){
    console.log(e.currentTarget.dataset.id);
    
    var index = e.currentTarget.dataset.id;
    if(index == this.data.playingObj.index && this.data.currentid == this.data.playingObj.tapid){
      audioPlayer.stop();
    }
    switch(this.data.currentid){
      case '0':
        {
          var fileList = this.data.fileList;    
          var audioDir = this.data.audioDir;
          fileManager.unlink({
            filePath:audioDir + "/" + fileList[index],
            success:function(res){
              console.log("删除成功:"+fileList[index]);
              fileList.splice(index,1);
              that.setData({
                fileList:fileList,
                clickedRow:{"index":-1,"tapid":-1}
              })
            },
            fail:function(res){
              console.log("删除失败:"+fileList[index]+"; "+res.errMsg);
            },
          });
        }
        break;
      case '1':
        {
          var ttsList = this.data.ttsList;    
          var ttsDir = this.data.ttsDir;
          fileManager.unlink({
            filePath:ttsDir + "/" + ttsList[index],
            success:function(res){
              console.log("删除成功:"+ttsList[index]);
              ttsList.splice(index,1);
              that.setData({
                ttsList:ttsList,
                clickedRow:{"index":-1,"tapid":-1}
              })
            },
            fail:function(res){
              console.log("删除失败:"+ttsList[index]+"; "+res.errMsg);
            },
          });
        }
      break;
      case '2':
        {
          var micRecordList = this.data.micRecordList;    
          var micRecordDir = this.data.micRecordDir;
          fileManager.unlink({
            filePath:micRecordDir + "/" + micRecordList[index],
            success:function(res){
              console.log("删除成功:"+micRecordList[index]);
              micRecordList.splice(index,1);
              that.setData({
                micRecordList:micRecordList,
                clickedRow:{"index":-1,"tapid":-1}
              })
            },
            fail:function(res){
              console.log("删除失败:"+micRecordList[index]+"; "+res.errMsg);
            },
          });
        }
      break;
      case '3':
      break;
    }
    
  },
  onRenameRowClicked:function(e){
    console.log("改名"+e.currentTarget.dataset.id);
    var index = e.currentTarget.dataset.id;
    var currentid = this.data.currentid;
    var dir = this.dirOfCurrentid(currentid);
    var fileName = this.nameOfCurrentidAndIndex(currentid,index);
    this.setData({
      renameObj : {"tapid":currentid,"index":index,"oldName":fileName,"newName":"","visible":true},
    })
  },
  onRenameCancel:function(e){
    this.setData({
      renameObj : {"tapid":-1,"index":-1,"oldName":"","newName":"","visible":false},
    })   
  },
  onRenameConfirm:function(e){
    var renameObj = this.data.renameObj;
    var currentid = renameObj.tapid;
    var dir = this.dirOfCurrentid(currentid);
    if(renameObj.newName.trim() == renameObj.oldName.trim() || renameObj.newName.trim() == ""){
      this.setData({
        renameObj : {"tapid":-1,"index":-1,"oldName":"","newName":"","visible":false},
      })
      return;
    }
    var fileList = this.fileListOfCurrentid(currentid);
    var dupIndex = fileList.indexOf(renameObj.newName);
    if(dupIndex >= 0){
      console.log("同名文件已经存在");
      wx.showToast({
        title: that.data.i18n["localaudio.tips.fileexists"],
        icon: 'none',
        duration: 2000
      });      
      return;
    }
    
    
    fileManager.rename({
      oldPath:dir + "/" + renameObj.oldName,
      newPath:dir + "/" + renameObj.newName,
      success:function(res){
        fileList[renameObj.index] = renameObj.newName;
        that.setFileListAtId(currentid,fileList);
      },
      fail:function(res){
        
      },
      complete:function(res){
        that.setData({
          renameObj : {"tapid":-1,"index":-1,"oldName":"","newName":"","visible":false},
          scroll_left:'0rpx',
        })
      }
    })
  },
  onTTSButtonClicked:function(e){
    
    var ttsDir = this.data.ttsDir;
    try {
      fileManager.accessSync(ttsDir);
      console.log(ttsDir+"目录已经存在")
    } catch (error) {
      fileManager.mkdirSync(ttsDir,true);
    }
    wx.navigateTo({
      url: '../tts/tts?ttsList='+ that.data.ttsList,
      events: {
        // 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
        acceptDataFromOpenedPage: function (data) {
          console.log(data)
          if (data.ttsNewFile != undefined){
            that.data.ttsList.push(data.ttsNewFile);
            that.setData({
              ttsList: that.data.ttsList,
            })
          }
        },
      },
    })
  },
  onShareButtonClicked:function(e){
    var fileList = this.data.fileList;
    var filePath = this.data.audioDir + "/" + fileList[0];
    wx.openDocument({
      filePath: filePath ,
      success:function(res){
        console.log("打开成功");
      },
      fail:function(res){
        console.log("打开失败");
      },
    })
  },
  newNameInput:function(e){
    var newName = e.detail.value;
    this.data.renameObj.newName = newName;
  },
  newNameOfDuplicatFile:function(fileList, elementname){
    var index = 0;
    var lastPoint = elementname.lastIndexOf(".");
    var name = elementname.substr(0,lastPoint);
    var ext = elementname.substr(lastPoint + 1, 5);
    var newname = name;
    while (fileList.indexOf(newname+"."+ext) >= 0) {
      index ++;
      newname = name + "-" + index;
    }
    return newname+"."+ext;
  },
  //根据tapid和index获取当前文件完整路径;必须先使用configFileDir
  nameOfCurrentidAndIndex:function(tapid, index){
    // var dir = dirOfCurrentid(tapid);
    var dirFiles = null;
    switch(tapid){
      case '0':
        dirFiles = this.data.fileList;
        break;
      case '1':
        dirFiles = this.data.ttsList;
        break;
      case '2':
        dirFiles = this.data.micRecordList;
        break;
      case '3':
        break;
    }
    if(dirFiles != null && index < dirFiles.length){
      return dirFiles[index];
    }
    return null;
  },
  //根据tapid获取当前文件目录
  dirOfCurrentid:function(tapid){
    var dir = wx.env.USER_DATA_PATH + "/";
    switch(tapid){
      case '0':
        dir = dir + "AudioList";
        break;
      case '1':
        dir = dir + "TTSList";
        break;
      case '2':
        dir = dir + "micRecordList";
        break;
      case '3':
        dir = null;
        break;
    }
    return dir;
  },
  //根据tapid获取当前文件列表
  fileListOfCurrentid:function(tapid){
    var fileList = [];
    switch(tapid){
      case '0':
        fileList = this.data.fileList;
        break;
      case '1':
        fileList = this.data.ttsList;
        break;
      case '2':
        fileList = this.data.micRecordList;
        break;
      case '3':
        break;
    }
    return fileList;
  },
  setFileListAtId:function(tapid, fileList){
    switch(tapid){
      case '0':
        this.setData({
          fileList:fileList,
        })
        break;
      case '1':
        this.setData({
          ttsList:fileList,
        })
        break;
      case '2':
        this.setData({
          micRecordList:fileList,
        })
        break;
      case '3':
        break;
    }
  },
  configFileDir:function(e){
    var audioDir = wx.env.USER_DATA_PATH + "/AudioList";
    this.setData({
      audioDir:audioDir,
    })
    fileManager = wx.getFileSystemManager();
    fileManager.readdir({
      dirPath:audioDir,
      success:function(res){
        that.setData({
          fileList:res.files,
        })
      },
      fail:function(res){

      }
    });

    var ttsDir = wx.env.USER_DATA_PATH + "/TTSList";
    this.setData({
      ttsDir:ttsDir,
    });
    fileManager.readdir({
      dirPath:ttsDir,
      success:function(res){
        that.setData({
          ttsList:res.files,
        })
      },
      fail:function(res){

      }
    });

    var micRecordDir = wx.env.USER_DATA_PATH + "/micRecordList";
    this.setData({
      micRecordDir:micRecordDir,
    });
    fileManager.readdir({
      dirPath:micRecordDir,
      success:function(res){
        that.setData({
          micRecordList:res.files,
        })
      },
      fail:function(res){

      }
    });

  },
  configAudioPlay:function(e){
    audioPlayer = wx.createInnerAudioContext();

    audioPlayer.onPlay(function(e){      
      var playingObj = that.data.playingObj;// = {"tapid":currentid,"index":index,"name":name,"state":"start"};
      playingObj.state = "playing";
      that.setData({
        playingObj : playingObj,
      })
      console.log("正在播放"+playingObj.tapid+","+playingObj.index);
      audioPlayer.onTimeUpdate(() => {
        //小于1秒的音频,再次播放后,会不触发事件.原因见官方bug.https://developers.weixin.qq.com/community/develop/doc/00068a72a2c588d3c6c8edeac56800       
        if(audioPlayer != null){
          console.log("总时长" + audioPlayer.duration)
          console.log("当前播放进度" + audioPlayer.currentTime)
        }
        if(playingObj.state == "playing" && playingObj.tapid == that.data.playingObj.tapid && playingObj.index == that.data.playingObj.index){
          if(playingObj.duration != null){
            playingObj.duration = audioPlayer.duration.toFixed(2);
            playingObj.currentTime = audioPlayer.currentTime.toFixed(2);
            that.setData({
              playingObj:playingObj,
            })
          }          
        }
        
      })
      //正在录音时,播放就不让隐藏.
      if(that.data.deviceRecordState == 0){
        wx.hideLoading({
          complete: (res) => {},
        });
      }
    });
    
    audioPlayer.onStop(function(e){
      that.setData({
        playingObj : {"tapid":that.data.currentid,"index":-1,"name":"","state":"stop","duration":that.data.playingObj.duration,"currentTime":that.data.playingObj.currentTime},
      })
      // if(that.data.deviceRecordState == 1 && eventChannel != null){
      //   eventChannel.emit('onDeviceRecord', {"test":"停止录音,收到停止录音后,再停止播放"});
      // }
    });
    audioPlayer.onEnded(function(e){  
      that.setData({
        playingObj:{"tapid":that.data.currentid,"index":-1,"name":"","state":"ended","duration":that.data.playingObj.duration,"currentTime":that.data.playingObj.duration},
      })
      if(that.data.deviceRecordState == 1 && eventChannel != null){
        eventChannel.emit('onDeviceRecord', {"test":"停止录音,收到停止录音后,再停止播放"});
      }
    });
  },
  configRecorder:function(e){
    recorderManager = wx.getRecorderManager();

    recorderManager.onStart(() => {
      console.log('recorder start')
    });

    recorderManager.onError((res) => {
      console.log(res);
      that.cancelRecord(that.data.i18n["record.error"]);

    })

    recorderManager.onStop((res) => {
      wx.hideToast();

      if (that.data.recordStatus!=2){return;}
      that.tempFilePath = res.tempFilePath;
      console.log('停止录音', res.tempFilePath)
      const { tempFilePath } = res
      that.saveRecordFile();
    })
  },
  saveRecordFile:function(e){

    var micRecordDir = this.data.micRecordDir;
    try {
      fileManager.accessSync(micRecordDir);
      console.log(micRecordDir+"目录已经存在")
    } catch (error) {
      fileManager.mkdirSync(micRecordDir,true);
    }
    var micRecordList = this.data.micRecordList;
    var fileName = util.formatTime(new Date()) +".aac";
    var destPath = that.data.micRecordDir + "/" + fileName;

    fileManager.saveFile({
      tempFilePath: that.tempFilePath,
      filePath:destPath,
      success:function(res){
          console.log("录音保存成功");
          micRecordList.push(fileName);
          micRecordList = micRecordList.sort(function(a,b){
            return a.localeCompare(b,'zh-CN');
          })
          that.setData({
            micRecordList:micRecordList,
          })
      },
      fail:function(res){
        console.log("录音保存失败");
        if(res.errMsg == "saveFile:fail the maximum size of the file storage limit is exceeded"){
          that.showDialog(true,that.data.i18n['localaudio.tip.maxsizelimted']);
        }else{
          that.showDialog(true,res.errMsg);
        }
      },
      complete:function(res){
        wx.showToast({
          title: that.data.i18n["record.saveing"],
          icon: 'loading',
          mask: true,
          duration: 99999
        });
    
        that.setData({recordStatus: 3 });
        wx.hideToast();
        // playFun();
      }
    })
  },
  touchStartRecordButton:function(e){
    if (that.data.recordStatus != 0 && that.data.recordStatus != 3) {
      return;
    }
  
    startTime = new Date().getTime();
  
    that.setData({ startPoint: [e.touches[0].pageX, e.touches[0].pageY] });
  
    timer = setTimeout(function () {
      this.touchStopRecordButton();
    }, maxLen * 1000);
  
  
    that.setData({
      recordStatus: 1
    });
    const options = {
      duration: 10000,//指定录音的时长，单位 ms
      sampleRate: 16000,//采样率
      numberOfChannels: 1,//录音通道数
      encodeBitRate: 32000,//编码码率
      format: 'aac',//音频格式，有效值 aac/mp3
      frameSize: 50,//指定帧大小，单位 KB
    }
    recorderManager.start(options);
  
    setTimeout(function(){
      if (that.data.recordStatus != 1)
      {
        return;
      }
      wx.showToast({
        title: that.data.i18n["record.recording"],
        icon: 'loading',
        duration: 300000
      });
    },200)
  },
  touchMoveRecordButton:function(e){
    if (that.data.recordStatus != 1) { return; }
    var curPoint = [e.touches[0].pageX, e.touches[0].pageY];
    var startPoint = this.data.startPoint;

    if ((curPoint[1] - startPoint[1]) < -50) {
      that.cancelRecord();
    }
  },
  cancelRecord:function(info = ''){
    clearTimeout(timer);
  
    if(that.data.recordStatus != 0)
    {
        recorderManager.stop();
    }
  
    that.setData({ recordStatus: 0 });
    setTimeout(function () {
        wx.showToast({
          title: (info == '') ? that.data.i18n["record.cancel"] : info,
          icon: 'none',
          duration: 1500
        });
     }, 200);
  
    console.log('end');
  },
  touchCancelRecordButton:function(e){
    console.log("touchCancel");    
  },
  touchStopRecordButton:function(e){
    clearTimeout(timer);

    if (that.data.recordStatus != 1) {
      return;
    }
  
    var duration = new Date().getTime() - startTime;
    if (duration < minLen * 1000) {
      if(wx.getStorageSync("language") == "zh_CN"){
        that.cancelRecord('时间不足' + minLen + "秒");
      }else{
        that.cancelRecord(that.data.i18n["record.soLessTime"]);
      }
      
      return;
    }
  
  
    setTimeout(function(){
      that.setData({
        recordStatus: 2
      });
  
      recorderManager.stop();
    }, 700);
  },
  tapBar:function(e){

    console.log(e.target.dataset.tapid)
    
    var that = this;
    var currentid = e.target.dataset.tapid;    
    var fileList = this.data.fileList;
    var ttsList = this.data.ttsList;
    var micRecordList = this.data.micRecordList;
    var clickedRow = this.data.clickedRow; 

    //为了修复苹果手机,列表不刷新的问题,只好把相差的数据,都重新设置一遍.
    that.setData({
      currentid:-1,
    })
    wx.showLoading({
      title: app.globalData.i18n["control.tips.loading"],
    })
    setTimeout(() => {
      that.setData({
        currentid:currentid,
      })
      wx.hideLoading({
        complete: (res) => {},
      })
    }, 200);
  },
  showDialog:function(visible, msg=""){
    this.setData({
      dialog:{visible:visible, msg:msg},
    })
  },
  closeDialog:function(e){
    this.setData({
      dialog:{visible:'hidden', msg:""},
    })
  },
})