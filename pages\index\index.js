// pages/control/control.js
var util = require('../../utils/util.js');
const langData = require('../../langData.js');
//读取默认语言
const lg = wx.getStorageSync('language');
Page({

    /**
     * 页面的初始数据
     */
  data: Object.assign({}, langData.data, {
        deviceName: "Waytronic",
        search: "搜索设备",
        action: "注意事项",
        action1: "1.搜索设备前，请打开手机蓝牙",
        action2: "2.找不到设备时，请确定设备已通电",
        action3: "3.初始化设备时，请勿异常退出配置流程",
        checkBLE: "请检测手机蓝牙是否打开",

        scanDevices: [],

        award_result_goods: [],
        award_result: [],
    lang: lg,
  }),

    searchtouch1: function () {
      // if (__wxConfig.envVersion =="develop"){
      //   // wx.switchTab({
      //   //   url: '../control/control'
      //   // })
      //   wx.switchTab({
      //     url: '../mine/mine',
      //   })
      //   return;
      // }
        wx.stopBluetoothDevicesDiscovery({
            complete: function (res) {
                wx.navigateTo({
                    url: '../bt/bt'
                })
            }
        })
        
    },

    searchtouch: function () {
        var that = this;
        //获取适配器
        wx.openBluetoothAdapter({
            success: function (res) {
                // success
                console.log("-----success----------");
                console.log(res);
                //开始搜索
                wx.startBluetoothDevicesDiscovery({
                    services: [],
                    success: function (res) {
                        // success
                        console.log("-----startBluetoothDevicesDiscovery--success----------");
                        console.log(res);

                        wx.onBluetoothDeviceFound(function (obj) {
                            // console.dir(obj.devices[0])
                            var temp = that.data.scanDevices
                            if (obj.devices[0].name) {
                                obj.devices.map(dev => {
                                    let pDev = temp.find((it) => {
                                        return it.deviceId == dev.deviceId
                                    })
                                    if (!pDev) {
                                        temp.push(dev)
                                        console.log(dev);
                                    }
                                })
                            }
                            that.setData({
                                scanDevices: temp
                            })
                        })
                    },
                    fail: function (res) {
                        // fail
                        console.log(res);
                    },
                    complete: function (res) {
                        // complete
                        console.log(res);
                    }
                })
            },
            fail: function (res) {

                wx.showModal({
                  title: that.data.i18n["Tips"],
                  content: that.data.i18n["bt.blecheck"],
                    showCancel: false
                })
            },
        })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        console.log('onLoad')
        var that = this;
        util.resetSetData.call(this, langData);
        //wx.setStorageSync("i18n", this.data.i18n);
        getApp().globalData.i18n = this.data.i18n;

        this.setData({
          search: this.data.i18n["index.search"],
          action: this.data.i18n["index.action"],
          action1: this.data.i18n["index.action1"],
          action2: this.data.i18n["index.action2"],
          action3: this.data.i18n["index.action3"],
          checkBLE: this.data.i18n["bt.blecheck"],
        })
      var title = this.data.i18n["Tips"];
      var content = this.data.i18n["bt.blecheck"];
        //获取适配器
        wx.openBluetoothAdapter({
            success:function(res){
              console.log("success");
            },
            fail: function (res) {
              console.log("fail");            
                            
                wx.showModal({
                  title: title,                  
                  content: content,
                    showCancel: false
                })
            },
        })
      
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {

    },

    onHide: function () {
        console.log(">>>onHide")
        wx.stopBluetoothDevicesDiscovery({
            success: function (res) {
                console.log(res)
            }
        })
    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
        console.log(">>>onUnload")
        wx.closeBluetoothAdapter({
            success: function (res) {
                console.log(res)
            }
        })
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    },

    onIconClicked:function(){
        wx.navigateTo({
          url: "../localaudio/localaudio"
        })
        // wx.switchTab({
        //   url: '../mine/mine',
        // })
    }
})