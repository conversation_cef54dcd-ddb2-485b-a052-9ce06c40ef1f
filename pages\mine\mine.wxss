/* pages/mine/mine.wxss */
/* pages/mine/mine.wxss */

.imagesize{
 display:flex;       
 height: 100px;            
 justify-content: flex-start;
 align-items: center;        
}
.imagesize image { 
  width:100rpx;
  height:100rpx;
  margin: 20px;
  }

.textsize { 
    display:flex;
    align-items: center;
  margin: 5px;

  } 

.textsize text { 
    font-size: 35rpx;
  margin: 3px;
  }  

.divLine{
    background: #E0E3DA;
    height: 3rpx;
}

.imagesize1{
    width:20rpx;
    height:30rpx;
}

.arrow{
 
width: 10px;
 
height: 10px;
 
border-top: 2px solid #999;
 
border-right: 2px solid #999;
 
position: absolute;
 
right: 20rpx;
 
transform: rotate(45deg);
 
/* margin-top:10rpx; */
 
}